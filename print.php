<?php
ob_start();
session_start();
if (isset($_SESSION['LoginToAdminIdentity'])) {
    require '../init.php';
    // Check if get request id is numeric & get the integer value of it
    $id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
    // Select all data depend on this ID
    $query = 'SELECT * FROM identityalmkhtar WHERE id = ? LIMIT 1';
    $row = $db->Fetch($query, [$id]);
    // The row count
    $count = $db->RowCountData($query, [$id]);

    // If there's such ID show the form
    if ($count > 0) { ?>
        <!doctype html>
        <html lang="ar" dir="rtl">

        <head>
            <!-- Required meta tags -->
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

            <!-- Bootstrap CSS -->
            <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
            <title>نافذة التقديم على هوية المختار</title>
            <style>
                .content p {
                    font-size: 28px;
                }
            </style>
        </head>

        <body>
            <div class="container">
                <div class="printer">
                    <div class="header text-center">
                        <h2>استمارة هويات المختارين</h2>
                        <h2> قسم شؤون المواطنين شعبة المختارين</h2>
                        <h2>رقم الاستمارة (1)</h2>
                    </div>
                    <hr>
                    <div class="content text-right mt-5">
                        <div class="row">
                            <div class="col-8">
                                <p>رقم الطلب: <strong><?php echo $id ?></strong></p>
                                <p>الاسم الرباعي: <strong><?php echo $row['fullname'] ?></strong></p>
                                <p>مختار منطقة : <strong><?php echo $row['elimination']  . ' ' . $row['side'] ?></strong></p>
                                <p><?php echo $row['prefixvillage'] ?> : <strong><?php echo $row['village'] ?></strong></p>
                                <p>رمز ورقم الهوية : <strong><?php echo $row['code_number_identity'] ?></strong></p>
                                <p>رمز ورقم الختم : <strong><?php echo $row['number_of_stamp'] ?></strong></p>
                                <p>تاريخ الولادة : <strong><?php echo $row['date_of_birth'] ?></strong></p>
                                <p>محل الولادة : <strong><?php echo $row['goverment'] ?></strong></p>
                                <p>رقم الهاتف : <strong><?php echo $row['phone_number'] ?></strong></p>
                                <p>فصيلة الدم : <strong><?php echo $row['blood_type'] ?></strong></p>
                                <p>تاريخ الاصدار : <strong><?php echo $row['created_at'] ?></strong></p>
                                <p>تاريخ النفاذ : <strong><?php echo $row['expiration_date'] ?></strong></p>
                                <?php
                                $identitiesupdatescount = $db->FetchAll("SELECT * FROM identities_updates_count WHERE form_id = ? AND type = '3' ORDER BY id DESC", [$row['id']]);
                                foreach ($identitiesupdatescount as $key => $value) {
                                    if (!empty($value['created_at'])) {
                                        echo '<p>' . date('Y-m-d', strtotime($value['created_at'])) . '</p>';
                                    }
                                }

                                ?>
                            </div>
                            <div class="col-4 text-center">
                                <div>
                                    <img width="170" src="https://kirkuk.gov.iq/public/storage/<?php echo $row['photo'] ?>">
                                    <img width="250" class="mt-5" src="logo.gif">
                                </div>
                                <div class="mt-4">
                                    <?php
                                    if (!empty($row['fingerprint'])) {
                                        echo '<img width="100" src="uploads/fingerprint/' . $row['fingerprint'] . '">';
                                    }
                                    ?>

                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row" style="margin-top: 200px;">
                                <div class="col-6 text-center">
                                    <p><strong>سواره قابل خورشيد</strong></p>
                                    <p><strong>مسؤول شعبة المختارين</strong></p>
                                </div>
                                <div class="col-6 text-center">
                                    <p><strong>عمر مصطفى فقي محمد</strong></p>
                                    <p><strong>مدير قسم شؤون المواطنين</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
        </body>

        </html>
<?php
    } else {
        header('Location:dashboard.php');
        exit();
    }
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
