<?php
require '../init.php';
$id = $_POST['getid'];
$status = $_POST['statusSelect'];
if (isset($id) || isset($status)) {
    $output = array();
    $update_status = $db->Fetch("SELECT * FROM identityalmkhtar WHERE id = ?", [$id]);
    if ($status == 'انجزت') {

        if ($update_status['status'] == 'تجديد') {
            $db->Insert("INSERT INTO `identities_updates_count`( `form_id`, `type`, `created_at` ) VALUES ( :form_id, :ztype, :created_at )", [
                'form_id' => $id,
                'ztype' => '3',
                'created_at' => date('Y-m-d H:i:s'),
            ]);
        }
    }
    if ($status == "تجديد") {

        $db->Update("Update identityalmkhtar set `corel` = :corel where id = :id", [
            'id' => $id,
            'corel' => NULL
        ]);
    }
    $db->Update("Update identityalmkhtar set `status` = :status where id = :id", [
        'id' => $id,
        'status' => $status
    ]);
    $output['status'] = $status;
    $output['id'] = $id;
    echo json_encode($output);
}
