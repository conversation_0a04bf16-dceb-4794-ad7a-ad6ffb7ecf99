<?php
ob_start();
session_start();
date_default_timezone_set("Asia/Baghdad");
if (isset($_SESSION['LoginToAdminIdentity'])) {
    require '../init.php';



?>
    <!doctype html>
    <html lang="ar" dir="rtl">

    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <!-- Bootstrap CSS -->
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
        <link rel="stylesheet" href="all.min.css">
        <link rel="stylesheet" href="style.css">
        <title>نافذة التقديم على هوية المختار</title>
    </head>

    <body>
        <div style="display: inline-grid;position: fixed;left: 0;top: 50%;">
            <a href="excel.php" class="btn btn-success mb-2">تحميل ملف Excel</a>
            <button type="button" id="formreset" class="btn btn-primary mb-2">افراغ الحقول</button>
            <button type="button" class="btn btn-primary mb-2" data-toggle="modal" data-target="#FaceidModal">بصمة وجه</button>
            <button type="button" class="btn btn-info FingerMatchBtn" data-toggle="modal" data-target="#FingerMatchModal">بصمة اصبع</button>
        </div>
        <div class="container-fluid" style="width: 80%!important;">
            <div class="header">
                <div class="row">
                    <div class="col-md-6 text-right">
                        <h4 style="margin-right: 3px;">ديوان محافظة كركوك</h4>
                        <h6 style="margin-right: 38px;">قسم شؤون المواطنين</h6>
                        <h6 style="margin-right: 58px;">شعبة المختارين</h6>
                        <h6 style="margin-right: 68px;">لوحة التحكم</h6>
                        <h6>نافذة التقديم على هوية المختار</h6>
                        <a href="logout.php"><i class="fas fa-sign-out-alt fa-2x text-danger mt-3" style="margin-right: 65px;" data-toggle="tooltip" data-placement="bottom" title="تسجيل خروج"></i></a>
                        <a target="_blank" href="https://www.kik.gov.iq/form/identity-almkhtar"><i class="fas fa-home fa-2x text-primary mt-3" style="margin-right: 10px;" data-toggle="tooltip" data-placement="bottom" title="الانتقال الى الاستمارة"></i></a>
                    </div>
                    <div class="col-md-6">
                        <a href="https://id.kik.gov.iq/dashboard.php"><img style="max-width: 242px;" src="../asset/img/logo.webp"></a>
                    </div>
                </div>
            </div>
            <div class="content text-center">
                <div style="padding-top: 17px">
                    <div class="advancedSearch">
                        <form id="search_form">
                            <div class="form-group row text-right">
                                <div class="col-sm-4">
                                    <label for="label1">رقم الاستمارة</label>
                                    <input type="text" class="form-control" id="label1">
                                </div>
                                <div class="col-sm-4">
                                    <label for="label2">الاسم الرباعي</label>
                                    <input type="text" class="form-control" id="label2">
                                </div>
                                <div class="col-sm-4">
                                    <label for="label3">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="label3">
                                </div>
                                <div class="col-sm-4">
                                    <label for="label4">الاجراء</label>
                                    <select id="label4" class="form-control">
                                        <option value="">...</option>
                                        <option value="قيد المراجعة">قيد المراجعة</option>
                                        <option value="قيد الانجاز">قيد الانجاز</option>
                                        <option value="انجزت">انجزت</option>
                                        <option value="تجديد">تجديد</option>
                                    </select>

                                </div>
                                <div class="col-sm-4">
                                    <label for="label5">رمز ورقم الهوية</label>
                                    <input type="text" class="form-control" id="label5">
                                </div>
                                <div class="col-sm-4">
                                    <label for="label6">رمز ورقم الختم</label>
                                    <input type="text" class="form-control" id="label6">
                                </div>
                            </div>

                        </form>
                    </div>
                    <div class="table-responsive" id="dynamic_content">

                    </div>
                    <div style="clear:both"></div>
                </div>
            </div>
        </div>
        <?php
        if ($_SESSION['LoginToAdminIdentityID'] != 18) {
        ?>
            <!-- Modal Count Status -->
            <div class="modal fade" id="CountStatusModal" tabindex="-1" aria-labelledby="CountStatusModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title m-auto" id="CountStatusModalLabel">احصائيات الاجراء</h5>
                        </div>
                        <div class="modal-body text-right">
                            <div class="row text-center">
                                <div class="col sm-3">
                                    <p style="font-size: 15px;"><b>قيد المراجعة </b> <br>
                                    </p>
                                    <h4><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد المراجعة'") ?>
                                    </h4>
                                    <p></p>
                                </div>
                                <div class="col sm-3">
                                    <p style="color: #ffc107;font-size: 15px;"><b>قيد الانجاز </b> <br>
                                    </p>
                                    <h4><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد الانجاز'") ?>
                                    </h4>
                                    <p></p>
                                </div>
                                <div class="col sm-3">
                                    <p style="color: #28a745;font-size: 15px;"><b>انجزت</b> <br>
                                    </p>
                                    <h4><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'انجزت'") ?>
                                    </h4>
                                    <p></p>
                                </div>
                                <div class="col sm-3">
                                    <p style="color: #007bff;font-size: 15px;"><b>تجديد</b> <br>
                                    </p>
                                    <h4><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'تجديد'") ?>
                                    </h4>
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- View Form Modal -->
            <div class="modal fade" id="viewFormModalLong" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-right">
                            <ul class="list-group">
                                <li id="msgCopy" class="list-group-item alert alert-success" style="display:none;">تم النسخ
                                </li>
                                <li class="list-group-item text-center f0"><img width="170" src=""></li>
                                <li class="list-group-item f1"></li>
                                <li class="list-group-item f2"></li>
                                <li class="list-group-item f3" id="copyf3"></li>
                                <li class="list-group-item f8" id="copyf8"></li>
                                <li class="list-group-item f888" id="copyf888"></li>
                                <li class="list-group-item f9" id="copyf9"></li>
                                <li class="list-group-item f10" id="copyf10"></li>
                                <li class="list-group-item f5"></li>
                                <li class="list-group-item f7"></li>
                                <li class="list-group-item f6"></li>
                                <li class="list-group-item f4"></li>
                                <li class="list-group-item f11"></li>
                                <li class="list-group-item f12"></li>
                                <li class="list-group-item qrcode text-center">
                                    <img src=''>
                                </li>
                                <li class="list-group-item fingerprint text-center">
                                    <img width="100" src=''>
                                </li>
                                <li class="list-group-item f13 text-center">
                                    <iframe src="" style="display:none;" id="printframe" name="frame"></iframe>
                                    <button type="button" class="btn btn-primary" onclick="frames['frame'].print()">طباعة</button>
                                </li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End View Form Modal -->
            <!-- Edit Form Modal -->
            <div class="modal fade" id="editFormModalLong" tabindex="-1" role="dialog" aria-labelledby="fullNameHeaderModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fullNameHeaderModalLabel">تعديل الاستمارة</h5>
                        </div>
                        <form method="POST" id="formSubmit" enctype="multipart/form-data">
                            <input type="hidden" id="getid" name="getid">
                            <div class="modal-body text-right">
                                <div class="form-group">
                                    <label for="Input0">رقم الاستمارة</label>
                                    <input type="text" class="form-control" readonly id="Input0">
                                </div>
                                <div class="form-group">
                                    <label for="Input1">الاسم الرباعي</label>
                                    <input type="text" class="form-control" id="Input1" name="fullname">
                                </div>
                                <div class="form-group">
                                    <label for="Input2">محل وتاريخ الولادة</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <select class="form-control " name="goverment" style="padding: 0!important;" id="Input2">
                                                <option value="كركوك">كركوك</option>
                                                <option value="بغداد">بغداد</option>
                                                <option value="البصرة">البصرة</option>
                                                <option value="ميسان">ميسان</option>
                                                <option value="ذي قار">ذي قار</option>
                                                <option value="الديوانية">الديوانية</option>
                                                <option value="المثنى">المثنى</option>
                                                <option value="النجف الاشرف">النجف الاشرف</option>
                                                <option value="كربلاء المقدسة">كربلاء المقدسة</option>
                                                <option value="بابل">بابل</option>
                                                <option value="واسط">واسط</option>
                                                <option value="ديالى">ديالى</option>
                                                <option value="صلاح الدين">صلاح الدين</option>
                                                <option value="نينوى">نينوى</option>
                                                <option value="الانبار">الانبار</option>
                                                <option value="اربيل">اربيل</option>
                                                <option value="دهوك">دهوك</option>
                                                <option value="سليمانية">سليمانية</option>

                                            </select>
                                        </div>
                                        <div class="col-md-8">
                                            <input type="date" class="form-control" name="databirth" id="Input22">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="Input3">فصيلة الدم</label>
                                    <input type="text" class="form-control" name="bloadtype" id="Input3">
                                </div>
                                <div class="form-group">
                                    <label for="Input4">رقم الهاتف</label>
                                    <input type="number" class="form-control" name="phonenumber" id="Input4">
                                </div>
                                <div class="form-group">
                                    <label for="Input5">القضاء</label>
                                    <input type="text" class="form-control" name="elimination" id="Input5">
                                </div>
                                <div class="form-group">
                                    <label for="Input55">الناحية</label>
                                    <input type="text" class="form-control" name="side" id="Input55">
                                </div>
                                <div class="form-group">
                                    <label for="Input555">القرية او المحلة</label>
                                    <input type="text" class="form-control" name="village" id="Input555">
                                </div>
                                <div class="form-group">
                                    <label for="Input6">رمز ورقم الهوية</label>
                                    <input type="text" class="form-control" name="numberidentity" id="Input6">
                                </div>
                                <div class="form-group">
                                    <label for="Input7">رمز ورقم الختم</label>
                                    <input type="text" class="form-control" name="numberstamp" id="Input7">
                                </div>
                                <div class="file-upload">
                                    <div class="image-upload-wrap" style="display: none;">
                                        <input class="file-upload-input" type='file' name="photo" onchange="readURL(this);" accept="image/*" />
                                        <input type="hidden" name="oldphoto" id="oldphoto">
                                        <div class="drag-text">
                                            <h3>اسحب او اضغط لرفع صورة شخصية</h3>
                                            <small class="text-danger">
                                                يجب ان يكون حجم الصورة كحد اقصى 500 كيلوبايت
                                            </small>
                                        </div>
                                    </div>
                                    <div class="file-upload-content" style="display: block;">
                                        <img class="file-upload-image" src="" alt="your image" />
                                        <div class="image-title-wrap">
                                            <button type="button" onclick="removeUpload()" class="remove-image">حذف
                                                الصورة</span></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer" style="justify-content: center!important;">
                                <button type="button" style="margin: 0 23px;" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                                <button type="submit" id="btnSubmit" class="btn btn-primary">تحديث</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Edit Form Modal -->
            <!-- Change Status Modal -->
            <div class="modal fade" id="ChangeStatusModalCenter" tabindex="-1" role="dialog" aria-labelledby="ChangeStatusModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="ChangeStatusModalCenterTitle">تغير حالة الاستمارة</h5>
                        </div>
                        <h6 id="getfullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <form method="POST" id="ChangeStatusForm">
                            <input type="hidden" name="getid" id="getidstatus">
                            <div class="modal-body text-center">
                                <select id="statusSelect" class="form-control" name="statusSelect" style="padding: 0!important;">
                                    <option value="قيد المراجعة">
                                        قيد المراجعة
                                    </option>
                                    <option value="قيد الانجاز">
                                        قيد الانجاز
                                    </option>
                                    <option value="انجزت">
                                        انجزت
                                    </option>
                                    <option value="تجديد">
                                        تجديد
                                    </option>
                                </select>
                            </div>
                            <div class="modal-footer">
                                <input type="submit" class="btn btn-outline-success m-auto" id="btnStatus" value="تحديث">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Change Status Modal -->
            <!-- Download Upload Pdf Modal -->
            <div class="modal fade" id="DownloadUploadPdfModalCenter" tabindex="-1" role="dialog" aria-labelledby="DownloadUploadPdfModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="DownloadUploadPdfModalCenterTitle">رفع او تحميل PDF</h5>
                        </div>
                        <h6 id="getpdffullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <div class="text-center" id="pdfIcon">

                        </div>
                        <div class="m-auto pt-3">
                            <button class="btn btn-danger" id="deletepdf" title="مسح"><i class="fas fa-trash"></i></button>
                        </div>
                        <form method="POST" id="DownloadUploadPdf">
                            <input type="hidden" name="getpdfid" id="getpdfid">
                            <div class="modal-body text-center">
                                <input type="file" name="file" id="file" class="form-control">
                            </div>
                            <div class="modal-footer">
                                <input type="submit" class="btn btn-outline-success m-auto" id="btnDownloadPdf" value="تحديث">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Download Upload Pdf Modal -->
            <!-- Download Upload Corel Modal -->
            <div class="modal fade" id="DownloadUploadCorelModalCenter" tabindex="-1" role="dialog" aria-labelledby="DownloadUploadPdfModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="DownloadUploadCorelModalCenterTitle">تحميل Corel</h5>
                        </div>
                        <h6 id="getcorelfullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <hr>
                        <div class="text-center" id="corelIcon">

                        </div>
                        <div class="m-auto pt-3 pb-3">
                            <button class="btn btn-danger" id="deletecorel" title="مسح"><i class="fas fa-trash"></i></button>
                        </div>
                        <?php
                        if ($_SESSION['LoginToAdminIdentity'] == 'w.site') {
                        ?>
                            <div class="modal-footer">
                                <a target="_blank" class="btn btn-outline-success m-auto mt-4" id="btnDownloadCorel">رفع</a>
                                <button type="button" class="btn btn-outline-secondary m-auto" data-dismiss="modal">اغلاق</button>
                            </div>
                        <?php
                        } else { ?>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary m-auto" data-dismiss="modal">اغلاق</button>
                            </div>
                        <?php }
                        ?>
                    </div>
                </div>
            </div>
            <!-- Take Face Id -->
            <div class="modal fade" id="TakeFaceId" tabindex="-1" role="dialog" aria-labelledby="TakeFaceIdTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="TakeFaceIdTitle">بصمة الوجه</h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="d-none faceidinfo">
                                <span class="form_id"></span>
                                <span class="form_fullname"></span>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <video id="webcam" autoplay playsinline width="300"></video>
                                    <canvas id="canvas" class="d-none"></canvas>
                                </div>
                                <div class="col-6">
                                    <img src="" id="download-photo" alt="" width="300">
                                </div>
                            </div>
                            <div class="mt-3">
                                <img src="" class="d-none" id="fetchImg">
                                <button type="button" class="btn btn-secondary fetchpic">جلب الصورة الشخصية</button>
                                <button type="button" class="btn btn-primary takepic">اخذ لقطة</button>
                                <button type="submit" class="btn btn-info uploadface">رفع</button>
                            </div>
                            <div class="msgAlert">

                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>
            <!-- View Face Id -->
            <div class="modal fade" id="FaceidModal" tabindex="-1" role="dialog" aria-labelledby="FaceidModalTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="FaceidModalTitle">بصمة الوجه</h5>
                        </div>
                        <div class="modal-body text-center position-relative" id="appendCanvas">
                            <video id="video" width="600" height="450" autoplay>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>

            <!-- View Finger Scanner -->
            <div class="modal fade" id="FingerMatchModal" tabindex="-1" role="dialog" aria-labelledby="FingerMatchModalTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="FingerMatchModalTitle">بصمة الاصبع</h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="messageStatusFinger text-center">

                            </div>
                            <div id="appenddata" style="padding: 32px;">

                            </div>
                            <div class="text-center stepfinger2">

                            </div>
                            <button type="button" class="btn btn-outline-primary" id="gettem">تحقق</button>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>
        <?php
        } else { ?>
            <!-- View Form Modal -->
            <div class="modal fade" id="viewFormModalLong" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-right">
                            <ul class="list-group">
                                <li id="msgCopy" class="list-group-item alert alert-success" style="display:none;">تم النسخ
                                </li>
                                <li class="list-group-item text-center f0"><img width="170" src=""></li>
                                <li class="list-group-item f1"></li>
                                <li class="list-group-item f2"></li>
                                <li class="list-group-item f3" id="copyf3"></li>
                                <li class="list-group-item f8" id="copyf8"></li>
                                <li class="list-group-item f888" id="copyf888"></li>
                                <li class="list-group-item f9" id="copyf9"></li>
                                <li class="list-group-item f10" id="copyf10"></li>
                                <li class="list-group-item f5"></li>
                                <li class="list-group-item f7"></li>
                                <li class="list-group-item f6"></li>
                                <li class="list-group-item f4"></li>
                                <li class="list-group-item f11"></li>
                                <li class="list-group-item f12"></li>
                                <li class="list-group-item qrcode text-center">
                                    <img src=''>
                                </li>
                                <li class="list-group-item fingerprint text-center">
                                    <img width="100" src=''>
                                </li>
                                <li class="list-group-item f13 text-center">
                                    <iframe src="" style="display:none;" id="printframe" name="frame"></iframe>
                                    <button type="button" class="btn btn-primary" onclick="frames['frame'].print()">طباعة</button>
                                </li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>
        <!-- End Download Upload Corel Modal -->
        <!-- jQuery first, then Popper.js, then Bootstrap JS -->
        <script src="../asset/js/jquery-3.7.1.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous">
        </script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous">
        </script>
        <script type="text/javascript" src="https://unpkg.com/webcam-easy/dist/webcam-easy.min.js"></script>
        <script src="all.min.js"></script>
        <script src="face-api.min.js"></script>
        <script src="style.js"></script>
        <?php
        if ($_SESSION['LoginToAdminIdentityID'] != 18) {

            $gfg_folderpath = 'uploads/face/';
            $arrfile = array();
            // CHECKING WHETHER PATH IS A DIRECTORY OR NOT
            if (is_dir($gfg_folderpath)) {
                // GETTING INTO DIRECTORY
                $files = opendir($gfg_folderpath); {
                    // CHECKING FOR SMOOTH OPENING OF DIRECTORY
                    if ($files) {
                        //READING NAMES OF EACH ELEMENT INSIDE THE DIRECTORY
                        while (($gfg_subfolder = readdir($files)) !== FALSE) {
                            // CHECKING FOR FILENAME ERRORS
                            if ($gfg_subfolder != '.' && $gfg_subfolder != '..') {
                                array_push($arrfile, $gfg_subfolder);
                            }
                        }
                    }
                }
            }

        ?>
            <script>
                var folderlist = [];
                <?php
                foreach ($arrfile as $key => $value) {

                ?>
                    folderlist.push("<?php echo $value; ?>")
                <?php
                }

                ?>

                $("#FaceidModal").on('show.bs.modal', function() {
                    const video = document.getElementById('video');

                    Promise.all([
                        faceapi.nets.ssdMobilenetv1.loadFromUri('models'),
                        faceapi.nets.faceRecognitionNet.loadFromUri('models'),
                        faceapi.nets.faceLandmark68Net.loadFromUri('models'),
                    ]).then(startWebcam);

                    function startWebcam() {
                        navigator.mediaDevices
                            .getUserMedia({
                                video: true,
                                audio: false,
                            })
                            .then(stream => {
                                video.srcObject = stream;
                            })
                            .catch(error => {
                                console.error(error);
                            });
                    }

                    function getLabeledFaceDescriptions() {
                        const labels = folderlist;
                        return Promise.all(
                            labels.map(async label => {
                                const descriptions = [];
                                // for (let i = 1; i <= 2; i++) {
                                const img = await faceapi.fetchImage(`uploads/face/${label}/1.png`);
                                const detections = await faceapi
                                    .detectSingleFace(img)
                                    .withFaceLandmarks()
                                    .withFaceDescriptor();
                                if (detections != undefined && detections != null) {
                                    console.log(detections);
                                    descriptions.push(detections.descriptor);
                                }
                                // }
                                return new faceapi.LabeledFaceDescriptors(label, descriptions);
                            })
                        );
                    }
                    video.addEventListener('play', async () => {
                        const labeledFaceDescriptors = await getLabeledFaceDescriptions();
                        const faceMatcher = new faceapi.FaceMatcher(labeledFaceDescriptors);

                        const canvas = faceapi.createCanvasFromMedia(video);
                        document.getElementById("appendCanvas").append(canvas);

                        const displaySize = {
                            width: video.width,
                            height: video.height
                        };
                        faceapi.matchDimensions(canvas, displaySize);

                        setInterval(async () => {
                            const detections = await faceapi
                                .detectAllFaces(video)
                                .withFaceLandmarks()
                                .withFaceDescriptors();

                            const resizedDetections = faceapi.resizeResults(detections, displaySize);

                            canvas.getContext('2d', {
                                willReadFrequently: true
                            }).clearRect(0, 0, canvas.width, canvas.height);

                            const results = resizedDetections.map(d => {
                                return faceMatcher.findBestMatch(d.descriptor);
                            });
                            results.forEach((result, i) => {
                                const box = resizedDetections[i].detection.box;
                                const drawBox = new faceapi.draw.DrawBox(box, {
                                    label: result,
                                });
                                drawBox.draw(canvas);
                            });
                        }, 100);
                    });
                });
                $("#FaceidModal").on('hide.bs.modal', function() {
                    location.reload();
                });
                // Start Capture WebCam
                const webcamElement = document.getElementById('webcam');
                const canvasElement = document.getElementById('canvas');
                const webcam = new Webcam(webcamElement, 'user', canvasElement);

                $(document).on('click', '.TakeFaceId', function() {
                    webcam.start()
                        .then(result => {
                            console.log("webcam started");
                        })
                        .catch(err => {
                            console.log(err);
                        });
                    $('#TakeFaceId').modal('show');
                    $('#fetchImg').attr('src', 'https://kik.gov.iq/public/storage/' + $(this).attr('data-image'));
                    $('#TakeFaceIdTitle').text($(this).attr('data-fullname'));

                    var pathImg = 'uploads/face/' + $(this).attr('data-fullname') + '/1.png';

                    if (pathImg != "") {
                        $.ajax({
                            url: pathImg,
                            type: 'HEAD',
                            error: function() {
                                $('#download-photo').attr('src', 'uploads/No-Image-Placeholder.svg.png');
                            },
                            success: function() {
                                $('#download-photo').attr('src', pathImg);
                            }
                        });

                    }

                    $(".faceidinfo .form_id").text($(this).attr('id'));
                    $(".faceidinfo .form_fullname").text($(this).attr('data-fullname'));
                });
                $(".takepic").on('click', function() {
                    let picture = webcam.snap();
                    $("#download-photo").attr('src', picture);
                });

                $("#TakeFaceId").on('hide.bs.modal', function() {
                    webcam.stop();
                    $('#download-photo').attr('src', '');
                });
                $('.uploadface').on('click', function() {
                    var id = $(".faceidinfo .form_id").text(),
                        datafullname = $(".faceidinfo .form_fullname").text(),
                        photo = $("#download-photo").attr('src');
                    if ($('#download-photo').attr('src') != 'uploads/No-Image-Placeholder.svg.png') {
                        $.ajax({
                            url: 'upload_face.php',
                            method: 'POST',
                            data: {
                                id: id,
                                datafullname: datafullname,
                                photo: photo,
                            },
                            success: function(data) {
                                $(".msgAlert").html('<div class="alert alert-success mt-3" role="alert">تم حفظ بصمة الوجه بنجاح</div>');
                                setTimeout(() => {
                                    $(".msgAlert").html('');
                                }, 2000);
                            }
                        });
                    } else {
                        $(".msgAlert").html('<div class="alert alert-danger mt-3" role="alert">يرجى اخذ بصمة وجه!</div>');
                        setTimeout(() => {
                            $(".msgAlert").html('');
                        }, 2000);
                    }
                });
            </script>
            <script src="finger.js"></script>
        <?php
        }
        ?>
    </body>

    </html>
<?php
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
