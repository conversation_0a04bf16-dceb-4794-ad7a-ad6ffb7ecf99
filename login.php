<?php
session_start();
require '../init.php';
// Check if email coming from HTTP post request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email      = trim($_POST['InputEmail']);
    $pass       = $_POST['InputPassword'];
    // Check if the email exist in database
    $id = $db->Fetch("SELECT ID, Email, Password FROM users WHERE Email = ? AND Rules = 12 LIMIT 1", [$email]);
    if ($id == true && password_verify($pass, $id['Password'])) {
        $_SESSION['LoginToAdminIdentityAlmkhtar'] = $email; // Register Session Name 
        $_SESSION['LoginToAdminIdentityAlmkhtarID'] = $id['ID']; // Register Session ID
    } else {
        echo 'error';
    }
}
