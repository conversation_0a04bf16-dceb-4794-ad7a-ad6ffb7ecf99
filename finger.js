$(function () {
  window.URL = window.URL || window.webkitURL;
  var wsImpl = window.WebSocket || window.MozWebSocket;
  window.ws = new wsImpl('ws://localhost:8181/');
  ws.onmessage = function (event) {
    const obj = JSON.parse(event.data);

    $('#appendimg').html(
      "<img class='fingerprintbase64' src='data:image/png;base64," +
        obj[0] +
        "' width='100' >"
    );
    if (obj[1]) {
      $('.fingerPrintTemplate').html(obj[1]);
    }
    if (obj[2]) {
      $('.stepfinger').html('<h2>' + obj[2] + '</h2>');
      // if (obj[2] == 'already register') {
      //   $('.stepfinger').html(
      //     '<div class="alert alert-danger" role="alert">تم تسجيل هذه البصمة سابقاً.</div>'
      //   );
      //   setTimeout(() => {
      //     $('.stepfinger').html('');
      //   }, 3000);
      // }
      if (obj[2] == 'enroll succ') {
        $.ajax({
          method: 'POST',
          url: 'fingerprint.php',
          data: {
            getid_fingerprint: $('#getid_fingerprint').val(),
            fingerprintbase64: $('body .fingerprintbase64').attr('src'),
            fingerPrintTemplate: $('.fingerPrintTemplate').text(),
            type: 'enrollsucc',
          },
          success: function (data) {
            // console.log(data);
          },
        });

        $('.stepfinger').html(
          '<div class="alert alert-success" role="alert">تم تسجيل البصمة بنجاح.</div>'
        );
        setTimeout(() => {
          $('.stepfinger').html('');
        }, 3000);
      }
      if (obj[2] == 'enroll filed') {
        $('.stepfinger').html(
          '<div class="alert alert-danger" role="alert">فشل في تسجيل البصمة يرجى الضغط على نفس الاصبع.</div>'
        );
      }
      if (obj[2] == 'Verified') {
        $('.stepfinger2').html(
          '<div class="alert alert-success" role="alert">تم التحقق.</div>'
        );
      }
      if (obj[2] == 'Not Verified') {
        $('.stepfinger2').html(
          '<div class="alert alert-danger" role="alert">فشل التحقق.</div>'
        );
        $('#appenddata').html('');
      }
      if (obj[3]) {
        $.ajax({
          method: 'POST',
          url: 'fingerprint.php',
          data: {
            id: obj[3],
            type: 'fetchdata',
          },
          beforeSend: function () {
            $('#appenddata').html(
              '<div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div>'
            );
          },
          success: function (data) {
            $('#appenddata').html(data);
          },
        });
      }
    }
  };
  ws.onopen = function (e) {
    $('.messageStatusFinger').html('<p class="text-success">الجهاز متصل</p>');
    $('#startfinger').removeAttr('disabled');
    $('#startfinger').on('click', function () {
      $('.stepfinger').html('<h2>3</h2>');
    });
  };
  ws.onerror = function (e) {
    $('.messageStatusFinger').html(
      '<p class="text-danger">الجهاز غير متصل</p>'
    );
    $('#startfinger').attr('disabled', 'disabled');
  };
  $('#startfinger').on('click', function () {
    let data = ['startfinger', $('#getid_fingerprint').val()];

    ws.send(data);
  });
  $('#gettem').on('click', function () {
    let data = ['gettem', '0'];

    ws.send(data);
  });

  $('#TakeFingerScaneerModal').on('show.bs.modal', function () {
    $('#appendimg').html(
      '<img src="layout/images/1_ZGWXMiqjVcnuRXdytvgwmA.png" width="100" alt="">'
    );
    $('.stepfinger').html('');
    $('.fingerPrintTemplate').text('');
  });
  $('#FingerMatchModal').on('hide.bs.modal', function () {
    location.reload();
    load_data(1);
  });
});
