/**
 * Modern Enhancements for Admin Dashboard
 * Bootstrap 5.3 RTL with jQuery 3.6+ and Modern Flat Design
 */

$(document).ready(function() {
    'use strict';
    
    // Initialize modern features
    initializeModernFeatures();
    
    function initializeModernFeatures() {
        // Add smooth scrolling
        addSmoothScrolling();
        
        // Add button ripple effects
        addRippleEffects();
        
        // Add table enhancements
        enhanceTableInteractions();
        
        // Add form enhancements
        enhanceFormInputs();
        
        // Add loading states
        addLoadingStates();
        
        // Add notification system
        initializeNotifications();
    }
    
    // Smooth scrolling for anchor links
    function addSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 600, 'easeInOutCubic');
            }
        });
    }
    
    // Add ripple effect to buttons
    function addRippleEffects() {
        $('.btn').on('click', function(e) {
            const button = $(this);
            const ripple = $('<span class="ripple"></span>');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });
            
            button.append(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }
    
    // Enhance table interactions
    function enhanceTableInteractions() {
        // Add row selection highlight
        $(document).on('click', '.table tbody tr', function() {
            $(this).siblings().removeClass('table-active');
            $(this).addClass('table-active');
        });
        
        // Add column sorting indicators
        $(document).on('click', '.table thead th', function() {
            const $this = $(this);
            const isAsc = $this.hasClass('sort-asc');
            
            $('.table thead th').removeClass('sort-asc sort-desc');
            
            if (isAsc) {
                $this.addClass('sort-desc');
            } else {
                $this.addClass('sort-asc');
            }
        });
        
        // Add row hover effects with delay
        $(document).on('mouseenter', '.table tbody tr', function() {
            $(this).addClass('table-hover-active');
        }).on('mouseleave', '.table tbody tr', function() {
            $(this).removeClass('table-hover-active');
        });
    }
    
    // Enhance form inputs
    function enhanceFormInputs() {
        // Add floating labels effect
        $('.form-control, .form-select').on('focus blur', function() {
            const $this = $(this);
            const $label = $this.siblings('label');
            
            if ($this.val() || $this.is(':focus')) {
                $label.addClass('floating');
            } else {
                $label.removeClass('floating');
            }
        });
        
        // Add input validation styling
        $('.form-control').on('input', function() {
            const $this = $(this);
            const value = $this.val();
            
            if (value.length > 0) {
                $this.addClass('has-value');
            } else {
                $this.removeClass('has-value');
            }
        });
        
        // Add search input enhancements
        $('#search_form input, #search_form select').on('input change', function() {
            const $this = $(this);
            const hasValue = $this.val().length > 0;
            
            if (hasValue) {
                $this.addClass('search-active');
            } else {
                $this.removeClass('search-active');
            }
        });
    }
    
    // Add loading states to buttons and forms
    function addLoadingStates() {
        // Button loading states
        $('.btn').on('click', function() {
            const $btn = $(this);
            if (!$btn.hasClass('no-loading')) {
                $btn.addClass('btn-loading');
                setTimeout(() => {
                    $btn.removeClass('btn-loading');
                }, 2000);
            }
        });
        
        // Form submission loading
        $('form').on('submit', function() {
            const $form = $(this);
            const $submitBtn = $form.find('[type="submit"]');
            
            $submitBtn.addClass('btn-loading').prop('disabled', true);
            
            setTimeout(() => {
                $submitBtn.removeClass('btn-loading').prop('disabled', false);
            }, 3000);
        });
    }
    
    // Initialize notification system
    function initializeNotifications() {
        window.showNotification = function(message, type = 'info', duration = 3000) {
            const notification = $(`
                <div class="notification notification-${type} animate__animated animate__fadeInRight">
                    <div class="notification-content">
                        <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
                        <span>${message}</span>
                        <button type="button" class="btn-close" aria-label="Close"></button>
                    </div>
                </div>
            `);
            
            $('body').append(notification);
            
            // Auto remove
            setTimeout(() => {
                notification.addClass('animate__fadeOutRight');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
            
            // Manual close
            notification.find('.btn-close').on('click', function() {
                notification.addClass('animate__fadeOutRight');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            });
        };
        
        function getNotificationIcon(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
    }
    
    // Add keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl + F for search focus
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            $('#label1').focus();
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            $('#search_form')[0].reset();
            $('#search_form input, #search_form select').removeClass('search-active has-value');
            load_data(1);
        }
    });
    
    // Add scroll to top button
    const scrollToTopBtn = $(`
        <button class="scroll-to-top btn btn-primary" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    `);
    
    $('body').append(scrollToTopBtn);
    
    $(window).on('scroll', function() {
        if ($(this).scrollTop() > 300) {
            scrollToTopBtn.addClass('show');
        } else {
            scrollToTopBtn.removeClass('show');
        }
    });
    
    scrollToTopBtn.on('click', function() {
        $('html, body').animate({ scrollTop: 0 }, 600);
    });
    
    // Add theme toggle (optional)
    const themeToggle = $(`
        <button class="theme-toggle btn btn-outline-secondary" title="تبديل المظهر">
            <i class="fas fa-moon"></i>
        </button>
    `);
    
    $('.header .d-flex').append(themeToggle);
    
    themeToggle.on('click', function() {
        $('body').toggleClass('dark-theme');
        const icon = $(this).find('i');
        icon.toggleClass('fa-moon fa-sun');
    });
});
