<?php

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    require '../init.php';
    if ($_POST['type'] == 'enrollsucc') {
        $fingerprintbase64 = $_POST['fingerprintbase64'];
        $fingerprint_template = $_POST['fingerPrintTemplate'];
        $id = $_POST['getid_fingerprint'];

        if (!empty($id) && !empty($fingerprintbase64) && !empty($fingerprint_template)) {
            $image_array_1 = explode(";", $fingerprintbase64);
            $image_array_2 = explode(",", $image_array_1[1]);
            $fingerprintbase64 = base64_decode($image_array_2[1]);

            $fingerprint_Upload = $id . '_' . rand(0, 10000000) . '.png';

            file_put_contents("uploads/fingerprint/" . $fingerprint_Upload, $fingerprintbase64);

            $db->Update("UPDATE identityalmkhtar SET `fingerprint` = :fingerprint, `fingerprint_template` = :fingerprint_template WHERE id = :id", [
                'fingerprint' => $fingerprint_Upload,
                'fingerprint_template' => $fingerprint_template,
                'id' => $id
            ]);
        }
    }
    if ($_POST['type'] == 'fetchdata') {
        $id = $_POST['id'];
        $row = $db->Fetch("SELECT * FROM identityalmkhtar WHERE id = ? LIMIT 1", [$id]);

        echo '<div class="text-center">
            <img src="https://kik.gov.iq/public/storage/' . $row['photo'] . '" class="rounded-circle" width="170" alt="">
            <h2>' . $row['fullname'] . '</h2>
            <h5>' . $row['elimination'] . ' ' . $row['side'] . '</h5>
            <h5 class="text-start ps-5" style="color: #393185">القرية او الحي : <b style="color:#992727" class="notokufiarabic">' . (!empty($row['village']) ? $row['village'] : 'لايوجد') . '</b></h5>
            <h5 class="text-start ps-5" style="color: #393185">رمز ورقم الهوية : <b style="color:#992727" class="notokufiarabic">' . (!empty($row['code_number_identity']) ? $row['code_number_identity'] : 'لايوجد') . '</b></h5>
            <h5 class="text-start ps-5" style="color: #393185">رمز ورقم الختم : <b style="color:#992727" class="notokufiarabic">' . (!empty($row['number_of_stamp']) ? $row['number_of_stamp'] : 'لايوجد') . '</b></h5>
            <h5 class="text-start ps-5" style="color: #393185">تاريخ النفاذ : <b style="color:#992727" class="notokufiarabic">' . (!empty($row['expiration_date']) ? $row['expiration_date'] : 'لايوجد') . '</b></h5>
        </div>';
    }
}
