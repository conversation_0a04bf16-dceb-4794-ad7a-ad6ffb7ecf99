<?php
ob_start(); // Output Buffering Start

session_start();

if (isset($_SESSION['identitiesssssfsfegfhfhfhf'])) {
	require "db.php";
	require "view/header.php";
	require "function.php";

	//  Dashboard Page
$action = isset($_GET['action']) ? $_GET['action'] : 'dashboard';

if ($action == 'dashboard') { // Start dashboard 

	echo "<div class='container home-stats text-center'>"; 

	if (isset($_GET["page"])) { 
	 	$currentPage  = $_GET["page"]; 
	} else {
	 	$currentPage = 1;
	}

	$prevPage = $currentPage-1;
	$nextPage = $currentPage+1;

	$perPage = 50;
	$startPage = ($currentPage - 1) * $perPage;
	$limit = $startPage . ',' . $perPage;
	$lastPage = countItems("id", "identities");
	$resultPage = ceil($lastPage / $perPage);

	$stmt = $db->prepare("SELECT * FROM identities ORDER BY id DESC LIMIT $limit");
	$stmt->execute();
	$fetchAllForm = $stmt->fetchAll();
	?>
<!-- ========================= Start ============================= -->
<div class="text-center" style="float: left; margin-top: 20px; margin-bottom: 10px">
	<img src="https://www.kirkuk.gov.iq/content/uploads/logo.png" width="300">
</div>
<div class="upper text-right" style="float: right;padding-top: 96px;">
	<a href="https://www.kirkuk.gov.iq/form/identity" target="_blank">الذهاب الى الاستمارة</a> |
	<a href="logout.php">تسجيل خروج</a>
	<h1 style="margin-top: 22px; margin-right: 15px" class="h1-admin">لوحة التحكم</h1>
	<h6 class="text-center">استمارة اصدار هواية موظفي</h6>
	<h6 class="text-center">وباج دخول ديوان محافظة كركوك</h6>
</div>
		<div style="width: 200px; margin: auto;padding-top: 40px">
			<hr>
				<?php
					if ($_SERVER['REQUEST_METHOD'] == 'POST') {
						$status = $_POST['status'];

						$stmtStatus = $db->prepare("UPDATE status SET movingfurniture = ?");
						$stmtStatus->execute(array($status));

						$success = '<p class="text-success">تم تحديث حالة الاستمارة</p>';
					}
				?>
				<form action="<?php echo $_SERVER['PHP_SELF']?>" method="POST">
					<label>حاله الاستمارة</label>
					<select class="form-control" name="status">
						<?php
							$stmtSe = $db->prepare("SELECT movingfurniture FROM status");
							$stmtSe->execute();
							$statusSelected = $stmtSe->fetch();
						?>
						<option value="0" <?php if ($statusSelected['movingfurniture'] == '0') { echo 'selected' ; } ?>>مفتوح</option>
						<option value="1" <?php if ($statusSelected['movingfurniture'] == '1') { echo 'selected' ; } ?>>مغلق</option>
					</select>
					<?php if(isset($success)) {
						echo '<div id="hideMe">' . $success . '</div>';
					}  ?>
					<br/>
					<input type="submit" class="btn btn-outline-success btn-block" value="حفظ">
				</form>
			<hr>
		</div>
		<?php if(!empty($fetchAllForm)) { ?>

		<div style="padding-top: 50px">
			<input style="height: 48px;text-align: center;border-top-right-radius: 10px;border-top-left-radius: 10px;background-color: #f3efef;border: 1px solid #ccc; width: 684px;" type="text" name="search_text" id="search_text" placeholder="بحث  " />
			<div id="show_up"></div>
			<hr>
			<div class="clearfix">
		        <div class="hint-text">اظهار <b><?php echo $perPage ?></b> من اصل <b><?php echo $lastPage ?></b></div>
		        <ul class="pagination text-right">
		        	<?php 
		        		if ($currentPage == 1) {
		        			echo '<li class="page-item"><a>السابق</a></li>';
		        		} else {
		        			echo '<li class="page-item"><a class="page-link" href="?page=' . $prevPage . '">السابق</a></li>';
		        		}

		        		for ($i=$currentPage-2; $i <= $currentPage+2; $i++) { 
		        			if ($i > 0 && $i <= $resultPage) {
			        			if ($currentPage != $i) {
			        				echo '<li class="page-item"><a href="?page=' . $i . '" class="page-link ">' . $i . '</a></li>';
			        			} else {
			        				echo '<li class="page-item active"><a class="page-link ">' . $i . '</a></li>';
			        			}
			        		}
		        		}
		        	?>
		        	<?php 
		        		if ($currentPage == $resultPage) {
		        			echo '<li class="page-item"><a>التالي</a></li>';
		        		} else {
		        			echo '<li class="page-item"><a href="?page=' . $nextPage . '" class="page-link">التالي</a></li>';
		        		}
		        	?>
		        </ul>
		    </div>
			<div id="result"></div>
			<div style="clear:both"></div>
			<div class="clearfix">
		        <div class="hint-text">اظهار <b><?php echo $perPage ?></b> من اصل <b><?php echo $lastPage ?></b> </div>
		        <ul class="pagination text-right">
		        	<?php 
		        		if ($currentPage == 1) {
		        			echo '<li class="page-item"><a>السابق</a></li>';
		        		} else {
		        			echo '<li class="page-item"><a class="page-link" href="?page=' . $prevPage . '">السابق</a></li>';
		        		}

		        		for ($i=$currentPage-2; $i <= $currentPage+2; $i++) { 
		        			if ($i > 0 && $i <= $resultPage) {
			        			if ($currentPage != $i) {
			        				echo '<li class="page-item"><a href="?page=' . $i . '" class="page-link ">' . $i . '</a></li>';
			        			} else {
			        				echo '<li class="page-item active"><a class="page-link ">' . $i . '</a></li>';
			        			}
			        		}
		        		}
		        	?>
		        	<?php 
		        		if ($currentPage == $resultPage) {
		        			echo '<li class="page-item"><a>التالي</a></li>';
		        		} else {
		        			echo '<li class="page-item"><a href="?page=' . $nextPage . '" class="page-link">التالي</a></li>';
		        		}
		        	?>
		        </ul>
		    </div>	
		</div>	
		<?php } else {
			echo '<h1 style="padding-right: 28%;font-size: 27px;"> لاتوجد معاملات حالياً</h1>';	
		 }
		 echo "</div>";
	//  Dashboard Page
} elseif ($action == 'Info') {
	// Check if get request id is numeric & get the integer value of it
	$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
	// Select all data depend on this ID
	$stm = $db->prepare("SELECT * FROM identities WHERE id = ? LIMIT 1");
	// Execute Query
	$stm->execute(array($id));
	// Fetch the data
	$row = $stm->fetch();
	// The row count
	$count = $stm->rowCount();
	// If there's such ID show the form
	if ($count > 0) { 
		$counter = $db->prepare("UPDATE identities SET readable = ? WHERE id = ?");
		$counter->execute(array('1',$id));
		include('phpqrcode/qrlib.php');
		// outputs image directly into browser, as PNG stream
		QRcode::png('Ali Alhoasy');
		?>

	<div class="container">
		<div class="custom-form">
			<div class="text-center">
				<div>
					<img src="https://www.kirkuk.gov.iq/content/uploads/logo.png" width="200px">
				</div>
				<h4>استمارة اصدار هوية موظفي وباج دخول ديوان محافظة كركوك</h4>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<h6 class="text-right">رقم الاستمارة: <?php echo $row['id'] ?></h6>
				</div>
				<div class="col-sm-6">
					<h6 class="text-left"><?php echo date('Y-m-d', strtotime($row['created_at'])) ?></h6>
				</div>
			</div>
			<hr width="100%" style="margin: auto; padding-bottom: 20px; padding-top: 10px">
			<form action="{{ route('form.identity.store') }}" style="position: relative;" method="POST" enctype="multipart/form-data" >
				<div class="form-group row text-right" >
					<label class="col-sm-3 col-form-label">الحالة</label>
					<div class="col-sm-3">
						<input type="text" readonly value="<?php echo $row['f1'] ?>" class="form-control">
					</div>
				</div>
				<div class="form-group row text-right" >
					<label class="col-sm-3 col-form-label">تاريخ التقديم</label>
					<div class="col-sm-3">
						<input type="text" readonly value="<?php echo date("d-m-Y", strtotime($row['created_at'])) ?>" class="form-control">
					</div>
				</div>
				<div class="text-right">
				<div class="file-upload" style="position: absolute; left: 0;top: 0px;">
				  <div class="file-upload-content">
				    <img class="file-upload-image" src="../../public/storage/<?php echo $row['f2'] ?>" width="170" alt="your image" />
				  </div>
				  <?php 
				  	$getidentities22 = $db->prepare("SELECT LPAD(idn, 3, 0) as outputss FROM idnumber WHERE identities_id = ?");
				  	$getidentities22->execute(array($row['id']));
				  	$rowgetidentities22 = $getidentities22->fetch();
				  	if ($row['category'] != 'غير محدد') {
				  ?>
				  <h6 class="text-center" style="font-weight: bold; padding-top: 10px"><?php echo $row['category'] . '-' . $rowgetidentities22['outputss'] ?></h6>
				  <?php } ?>				  
				</div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">الاسم الثلاثي</label>
				    <div class="col-sm-3">
				    	<input type="text" readonly value="<?php echo $row['f3'] ?>" class="form-control">
				    </div>
				  </div>
				  <?php 
				  if (!empty($row['f4'])) { ?>
				  	<div class="form-group row">
				  	  <label class="col-sm-3 col-form-label">الاسم باللغة الانكليزية</label>
				  	  <div class="col-sm-3">
				  	  	<input type="text" readonly value="<?php echo $row['f4'] ?>" class="form-control">
				  	  </div>
				  	</div>
				  	<?php
				  }
				  ?>
				  <?php 
				  if (!empty($row['ff4'])) { ?>
				  <div class="form-group row">
					<label class="col-sm-3 col-form-label">سبب طلب باج الدخول</label>
				  <div class="col-sm-3">
					  <textarea class="form-control" readonly rows="3"><?php echo $row['ff4'] ?></textarea>
				  </div>
				</div>
				<?php
				}
				?>
				<?php 
				  if (!empty($row['ff5'])) { ?>
				<div class="form-group row">
				  <label class="col-sm-3 col-form-label">المهنة</label>
				  <div class="col-sm-3">
					  <input type="text" readonly value="<?php echo $row['ff5'] ?>" class="form-control">
				  </div>
				</div>
				  <?php
				  }
				  ?>
				  <div class="form-group row" >
				  	<?php 
				  	if ($row['f1'] == 'تنسيب') { ?>
				  		<label class="col-sm-3 col-form-label">اسم الدائرة المنسب منها</label>
				  	<?php
				  	}else {?>
				  		<label class="col-sm-3 col-form-label">العنوان الوظيفي</label>
				  	<?php
				  	}
				  	?>
				    <div class="col-sm-3">
				    	<input type="text" readonly value="<?php echo $row['f5'] ?>" class="form-control">
				    </div>
				  </div>
				  <div class="form-group row" >
				  	<?php 
				  	if ($row['f1'] == 'تنسيب') { ?>
				  	<label class="col-sm-3 col-form-label">القسم او الشعبة المنسب اليها</label>
				  	<?php
				  	}else {?>
				    <label class="col-sm-3 col-form-label">القسم او الشعبة</label>
				    <?php
				    }
				    ?>
				    <div class="col-sm-3">
				    	<input type="text" readonly value="<?php echo $row['f6'] ?>" class="form-control">
				    </div>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">هل تملك هوية او باج  دخول سابقاً </label>
				    <div class="col-sm-3">
						<input type="text" readonly value="<?php echo $row['f7'] ?>" class="form-control">
				    </div>
				  </div>
				  <div>
				  	<?php 
				  	if (!empty($row['f8'])) { ?>
				    <div class="form-group row">
				    	<label class="col-sm-3 col-form-label">رقم الهوية</label>
				    	<div class="col-sm-3">
				    		<input type="text" readonly value="<?php echo $row['f8'] ?>" class="form-control">
				    	</div>
				    </div>
				    <?php
				    }
				    ?>
				  </div>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">رقم الهاتف</label>
				    <div class="col-sm-3">
				    	<input type="number" readonly value="<?php echo $row['f10'] ?>" class="form-control">
				    </div>
				  </div>
				  <?php 
				  	if (!empty($row['f11'])) { ?>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">البريد الألكتروني ان وجد</label>
				    <div class="col-sm-3">
				    	<input type="email" readonly value="<?php echo $row['f11'] ?>" class="form-control">
				    </div>
				  </div>
				  <?php
				  }
				  ?>
				  <div class="form-group row">
				  	<?php 
				  	if ($row['f1'] == 'تنسيب') { ?>
				  	<label class="col-sm-3 col-form-label">تاريخ التنسيب</label>
				  	<?php
				  	}else {?>
				    <label class="col-sm-3 col-form-label" >تاريخ التعيين</label>
				    <?php
				    }
				    ?>
				    <div class="col-sm-3">
				    	<input type="text" readonly value="<?php echo $row['f12'] ?>" class="form-control">
				    </div>
				  </div>
				  <hr>
				  <div class="form-group row">
				    <label class="col-sm-2 col-form-label">عنوان السكن</label>
				    <label class="col-sm-1 col-form-label">المحافظة</label>
				    <div class="col-sm-2">
				    	<input type="text" readonly value="<?php echo $row['f13'] ?>" class="form-control">
				    </div>
				    <label class="col-sm-1 col-form-label">القضاء</label>
				    <div class="col-sm-2">
				    	<input type="text" readonly value="<?php echo $row['f14'] ?>" class="form-control">
				    </div>
				    <label class="col-sm-1 col-form-label">الناحيه</label>
				    <div class="col-sm-2">
				    	<input type="text" readonly value="<?php echo $row['f15'] ?>" class="form-control">
				    </div>
				  </div>

				  <div class="form-group row">
				  	<label class="col-sm-1 col-form-label">محلة</label>
				    <div class="col-sm-1">
				    	<input type="text" readonly value="<?php echo $row['f16'] ?>" class="form-control">
				    </div>
				    <label class="col-sm-1 col-form-label">زقاق</label>
				    <div class="col-sm-1">
				    	<input type="text" readonly value="<?php echo $row['f17'] ?>" class="form-control">
				    </div>
				    <label class="col-sm-1 col-form-label">دار</label>
				    <div class="col-sm-1">
				    	<input type="text" readonly value="<?php echo $row['f18'] ?>" class="form-control">
				    </div>
				    <label class="col-sm-2 col-form-label">اقرب نقطة دالة</label>
				    <div class="col-sm-4">
				    	<input type="text" readonly value="<?php echo $row['f19'] ?>" class="form-control">
				    </div>
				  </div>
				  <hr>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">هل تملك مركبة خاصة</label>
				    <div class="col-sm-3">
						<input type="text" readonly value="<?php echo $row['f20'] ?>" class="form-control">
				    </div>
				  </div>
				  <?php 
				  	if ($row['f20'] == 'نعم') { ?>
					<div class="form-group row">
						<div class="col-sm-4">
							<input type="text" readonly value="<?php echo $row['f22'] ?>" class="form-control" placeholder="اسم صاحب السنوية">
						</div>
						<div class="col-sm-2">
							<input type="text" readonly value="<?php echo $row['f23'] ?>" class="form-control" placeholder="رقمها">
						</div>
						<div class="col-sm-2">
							<input type="text" readonly value="<?php echo $row['f24'] ?>" class="form-control">
						</div>
						<div class="col-sm-2">
							<input type="text" readonly value="<?php echo $row['f25'] ?>" class="form-control" placeholder="نوعها">
						</div>
						<div class="col-sm-2">
							<input type="text" readonly value="<?php echo $row['f26'] ?>" class="form-control" placeholder="موديلها">
						</div>
					</div>
					<?php
					}
					?>
				  <div class="form-group row">
				    <label class="col-sm-3 col-form-label">هل تملك سلاح</label>
				    <div class="col-sm-3">
				    	<input type="text" readonly value="<?php echo $row['f27'] ?>" class="form-control">
				    </div>
				  </div>
				  <?php 
				  	if ($row['f27'] == 'نعم') { ?>
					<div class="form-group row">
						<div class="col-sm-3">
							<input type="text" readonly class="form-control" placeholder="نوع السلاح">
						</div>
						<div class="col-sm-3">
							<input type="text"  readonly class="form-control" placeholder="رقم السلاح ">
						</div>
						<div class="col-sm-3">
							<input type="text"  readonly class="form-control" placeholder="رقم الرخصة ">
						</div>
						<div class="col-sm-3">
							<input type="text" readonly  class="form-control" placeholder="التاريخ">
						</div>
					</div>
					<?php
					}
					?>
				</div>
				<hr>
				<div style="padding-top: 200px">
					<div class="row text-center">
						<div class="col-sm-4">
							<h6>مدير الموارد البشرية</h6>
						</div>
						<div class="col-sm-4">
							<h6>مسؤول امن ديوان المحافظة</h6>
						</div>
						<div class="col-sm-4">
							<h6>مكتب السيد المحافظ</h6>
						</div>
					</div>
				</div>
			</form>
			<button style='position: fixed; left: 0;top: 130px' class='btn btn-info' onclick="this.style.display='none';window.print();">طباعة</button>
		
			<?php 
				if(!empty($row['f9'])) {
					echo '<div class="text-center">';
						echo '<img class="d-block m-auto" width="80%" src="../../public/storage/'.$row['f9'].'">';
					echo '</div>';
				}
			?>
			<?php 
				if(!empty($row['f21'])) {
					echo '<div class="text-center">';
						echo '<img class="d-block m-auto" width="80%" src="../../public/storage/'.$row['f9'].'">';
					echo '</div>';
				}
			?>	
			<?php 
				if(!empty($row['f28'])) {
					echo '<div class="text-center">';
						echo '<img class="d-block m-auto" width="80%" src="../../public/storage/'.$row['f9'].'">';
					echo '</div>';
				}
			?>	
			<?php 
				if(!empty($row['f33'])) {
					echo '<div class="text-center">';
						echo '<img class="d-block m-auto" width="80%" src="../../public/storage/'.$row['f9'].'">';
					echo '</div>';
				}
			?>		
		</div>
	</div>
	<?php
	}
} elseif ($action == 'UploadAndDownload') {
	// Check if get request id is numeric & get the integer value of it
	$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
	// Select all data depend on this ID
	$stm = $db->prepare("SELECT * FROM identities WHERE id = ? LIMIT 1");
	// Execute Query
	$stm->execute(array($id));
	// Fetch the data
	$row = $stm->fetch();
	// The row count
	$count = $stm->rowCount();
	// If there's such ID show the form
	if ($count > 0) {
		$counter = $db->prepare("UPDATE identities SET readable = ? WHERE id = ?");
		$counter->execute(array('1',$id));
		$url = isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ? $_SERVER['HTTP_REFERER'] : 'index.php';

		$stm2 = $db->prepare("SELECT * FROM identitiesfile WHERE iden_id = ?");
		$stm2->execute(array($id));
		$row2 = $stm2->fetch();
		?>
		<div style="padding-top: 30px;padding-bottom: 20px; width: 400px; margin: auto; text-align: right;">
			<a class="btn btn-secondary" href="<?php echo $url; ?>"><i class="fas fa-long-arrow-alt-right"></i> رجوع</a>
		</div>
		<?php
		if ($_SESSION['identitiesssssfsfegfhfhfhf'] == '<EMAIL>') {
		?>
		<div class="text-center">
			<p>اسم مقدم الاستمارة: <?php echo '<b>'.$row['f3'].'</b>' ?></p>

			<form action="?action=UploadFile" method="POST" style="width: 300px; margin: auto;" enctype="multipart/form-data">
			<input type="hidden" name="idform" value="<?php echo $row['id'] ?>">
			<input type="hidden" name="idform2" value="<?php echo $row2['iden_id'] ?>">
			 <label for="exampleFormControlSelect1">رفع</label>
				<input type="file" required="required"  class="form-control" id="exampleFormControlSelect1" name="image">
				<br/>
				<input class="btn btn-outline-success" type="submit" value="رفع">
			<form>
			
			<?php 
				if (!empty($row2['file'])) { 
					echo '<hr>';
					echo '<div class="text-center">';	
						echo '<a href="../../content/uploads/identitiesfile/'.$row2['file'].'"><i class="fas fa-cloud-download-alt fa-3x"></i></a>';
					echo '</div>';
				} else {
					echo '<h5>لايوجد ملف</h5>';
				}
			?>
		</div>
	<?php
		} else {
			if (!empty($row2['file'])) { 
				echo '<div class="text-center" style="width: 300px; margin: auto; padding-top: 30px">';
				echo '<p>اسم مقدم الاستمارة: <b>'.$row['f3'].'</b></p>';
				echo '<hr>';
					echo '<a href="../../content/uploads/identitiesfile/'.$row2['file'].'"><i class="fas fa-cloud-download-alt fa-3x"></i></a>';
				echo '</div>';
			} else {
				echo '<h5 class="text-center">لايوجد ملف</h5>';
			}
		}
	}
}elseif ($action == 'UploadFile') {

	if ($_SERVER['REQUEST_METHOD'] == 'POST') {

			// Get Variables From The Form
			$idform   	    = $_POST['idform'];
			$idform2   	    = $_POST['idform2'];

			$imageName     = $_FILES['image']['name'];
			$imageSize     = $_FILES['image']['size'];
			$imageTmp 	   = $_FILES['image']['tmp_name'];
			$imagetype	   = $_FILES['image']['type'];

			$imageUpload = rand(0, 1000) . '_' . $imageName;
			move_uploaded_file($imageTmp, "../../content/uploads/identitiesfile/" . $imageUpload);

			if ($idform2 == $idform) {

				$counter = $db->prepare("UPDATE identitiesfile SET file = ? WHERE iden_id = ?");
				$counter->execute(array($imageUpload ,$idform));
				// Echo Success Message
				$theMsg = '<div style="padding-top:20px" class="alert-custom"><div class="container text-center alert alert-success"><i class="fas fa-check"></i> تم تحديث الملف</div></div>';
				redirectHome($theMsg);
			} else {
				$stm = $db->prepare("INSERT INTO identitiesfile(file, iden_id, date) VALUES(:zfile, :ziden, now())");
				$stm->execute(array('zfile' => $imageUpload, 'ziden' => $idform));

				// Echo Success Message
				$theMsg = '<div style="padding-top:20px" class="alert-custom"><div class="container text-center alert alert-success"><i class="fas fa-check"></i> تم رفع الملف بنجاح</div></div>';
				redirectHome($theMsg);			
			}

	}
} elseif ($action == 'Actionform') {
	// Check if get request id is numeric & get the integer value of it
	$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
	// Select all data depend on this ID
	$stm = $db->prepare("SELECT * FROM identities WHERE id = ? LIMIT 1");
	// Execute Query
	$stm->execute(array($id));
	// Fetch the data
	$row = $stm->fetch();
	// The row count
	$count = $stm->rowCount();
	// If there's such ID show the form
	if ($count > 0) {
		$counter = $db->prepare("UPDATE identities SET readable = ? WHERE id = ?");
		$counter->execute(array('1',$id));
		$url = isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ? $_SERVER['HTTP_REFERER'] : 'index.php';
		?>
		<div style="padding-top: 30px;padding-bottom: 20px; width: 400px; margin: auto; text-align: right;">
			<a class="btn btn-secondary" href="<?php echo $url; ?>"><i class="fas fa-long-arrow-alt-right"></i> رجوع</a>
		</div>
		<div class="text-center">
			<p>اسم مقدم الاستمارة: <?php echo '<b>'.$row['f3'].'</b>' ?></p>
			<p>حالة الاستمارة حالياً : 
				<?php  
					if ($row['status'] == 'قيد المراجعة') {
						echo '<b>قيد المراجعة</b>';
					} elseif ($row['status'] == 'انجزت') {
						echo '<b class="text-success">انجزت</b>';
					}elseif ($row['status'] == 'قيد الانجاز') {
						echo '<b class="text-warning">قيد الانجاز</b>';
					}elseif ($row['status'] == 'رفض') {
						echo '<b class="text-danger">رفض</b>';
					}elseif ($row['status'] == 'نقص') {
						echo '<b class="text-danger">نقص</b>';
					}
				?>		
			</p>
			<form action="?action=UpdateActionform" method="POST">
			<input type="hidden" name="idform" value="<?php echo $row['id'] ?>">
			<?php 
				$getidentities = $db->prepare("SELECT * FROM idnumber WHERE identities_id = 0");
				$getidentities->execute();
				$rowgetidentities = $getidentities->fetch();
				$count = $getidentities->rowCount();
				if ($count > 0) {
					echo '<input type="hidden" name="getidentities" value="'.$rowgetidentities['idn'].'">';
				} 
			?>
			 <label for="exampleFormControlSelect1">تغير حالة المعاملة</label>
				<select style="width: 180px;margin-left: 5px;" class="custom-select" class="form-control" id="exampleFormControlSelect1" name="statusform">
				<option value="قيد المراجعة" <?php if ($row['status'] == 'قيد المراجعة') { echo 'selected' ; } ?>>قيد المراجعة</option>
				<option value="انجزت" <?php if ($row['status'] == 'انجزت') { echo 'selected' ; } ?>>انجزت</option>
				<option value="قيد الانجاز" <?php if ($row['status'] == 'قيد الانجاز') { echo 'selected' ; } ?>>قيد الانجاز</option>
				<option value="نقص" <?php if ($row['status'] == 'نقص') { echo 'selected' ; } ?>>نقص</option>
				<option value="رفض" <?php if ($row['status'] == 'رفض') { echo 'selected' ; } ?>>رفض</option>
				</select>
				<input class="btn btn-outline-success" type="submit" value="تحديث">
			<form> 
		</div>
	<?php
	}
}elseif ($action == 'UpdateActionform') {

	if ($_SERVER['REQUEST_METHOD'] == 'POST') {

			// Get Variables From The Form
			$idform   	    	= $_POST['idform'];
			$statusform   		= $_POST['statusform'];
			$getidentities   	= $_POST['getidentities'];

			$stmtget = $db->prepare("SELECT * FROM idnumber WHERE identities_id = ?");
			$stmtget->execute(array($idform));
			$rowid = $stmtget->fetch();

			if ($_POST['statusform'] == 'قيد الانجاز') {
				if (empty($getidentities)) {
					$stmidide = $db->prepare("INSERT INTO idnumber(identities_id) VALUES(:zcat)");
					$stmidide->execute(array(
						'zcat' 	=> $idform
					));
				} else {
					$stmidide3 = $db->prepare("UPDATE idnumber SET identities_id = ? WHERE idn = ?");
					$stmidide3->execute(array($idform, $getidentities));
				} 
			} elseif ($_POST['statusform'] == 'رفض') {
				if ($rowid['identities_id'] ==  $idform) {
					$stmidide = $db->prepare("UPDATE idnumber SET identities_id = 0 WHERE identities_id = ?");
					$stmidide->execute(array($idform));
				}
			} elseif ($_POST['statusform'] == 'نقص') {
				if ($rowid['identities_id'] ==  $idform) {
					$stmidide = $db->prepare("UPDATE idnumber SET identities_id = 0 WHERE identities_id = ?");
					$stmidide->execute(array($idform));
				}
			} elseif ($_POST['statusform'] == 'قيد المراجعة') {
				if ($rowid['identities_id'] ==  $idform) {
					$stmidide = $db->prepare("UPDATE idnumber SET identities_id = 0 WHERE identities_id = ?");
					$stmidide->execute(array($idform));
				}
			}

			$stm = $db->prepare("UPDATE identities SET status = ? WHERE id = ?");
			$stm->execute(array($statusform, $idform));

			// Echo Success Message

			$theMsg = '<div style="padding-top:20px" class="alert-custom"><div class="container text-center alert alert-success"><i class="fas fa-check"></i>
			تم تحديث البيانات بنجاح</div></div>';
			redirectHome($theMsg);
	}
} elseif ($action == 'Category') {
	// Check if get request id is numeric & get the integer value of it
	$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
	// Select all data depend on this ID
	$stm = $db->prepare("SELECT * FROM identities WHERE id = ? LIMIT 1");
	// Execute Query
	$stm->execute(array($id));
	// Fetch the data
	$row = $stm->fetch();
	// The row count
	$count = $stm->rowCount();
	// If there's such ID show the form
	if ($count > 0) {
		$counter = $db->prepare("UPDATE identities SET readable = ? WHERE id = ?");
		$counter->execute(array('1',$id));
		$url = isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ? $_SERVER['HTTP_REFERER'] : 'index.php';
		?>
		<div style="padding-top: 30px;padding-bottom: 20px; width: 400px; margin: auto; text-align: right;">
			<a class="btn btn-secondary" href="<?php echo $url; ?>"><i class="fas fa-long-arrow-alt-right"></i> رجوع</a>
		</div>
		<div class="text-center">
			<p>اسم مقدم الاستمارة: <?php echo '<b>'.$row['f3'].'</b>' ?></p>
			<p>حالة الاستمارة حالياً : 
				<?php  
					if ($row['category'] == 'غير محدد') {
						echo '<b>لم يتم تحديد فئة الاستمارة</b>';
					} elseif ($row['category'] == 'A') {
						echo '<b>A</b>';
					}elseif ($row['category'] == 'B') {
						echo '<b>B</b>';
					}elseif ($row['category'] == 'D') {
						echo '<b>D</b>';
					}elseif ($row['category'] == 'G') {
						echo '<b>E</b>';
					}
				?>		
			</p>
			<form action="?action=UpdateCategory" method="POST">
			<input type="hidden" name="idform" value="<?php echo $row['id'] ?>">
			 <label for="exampleFormControlSelect1">تغير حالة المعاملة</label>
				<select style="width: 111px;margin-left: 5px;" class="custom-select" class="form-control" id="exampleFormControlSelect1" name="statusform">
				<option value="غير محدد" <?php if ($row['category'] == 'غير محدد') { echo 'selected' ; } ?>>اختر</option>
				<option value="A" <?php if ($row['category'] == 'A') { echo 'selected' ; } ?>>A</option>
				<option value="B" <?php if ($row['category'] == 'B') { echo 'selected' ; } ?>>B</option>
				<option value="D" <?php if ($row['category'] == 'D') { echo 'selected' ; } ?>>D</option>
				<option value="G" <?php if ($row['category'] == 'G') { echo 'selected' ; } ?>>G</option>
				</select>
				<input class="btn btn-outline-success" type="submit" value="تحديث">
			<form> 
		</div>
	<?php
	}
}elseif ($action == 'GetAction') {
	// Check if get request id is numeric & get the integer value of it
	$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
	// Select all data depend on this ID
	$stm = $db->prepare("SELECT * FROM identities WHERE id = ? LIMIT 1");
	// Execute Query
	$stm->execute(array($id));
	// Fetch the data
	$row = $stm->fetch();
	// The row count
	$count = $stm->rowCount();
	// If there's such ID show the form
	if ($count > 0) {
		$counter = $db->prepare("UPDATE identities SET readable = ? WHERE id = ?");
		$counter->execute(array('1',$id));
		$url = isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ? $_SERVER['HTTP_REFERER'] : 'index.php';
		?>
		<form action="?action=SendAction" method="POST" style="margin: auto; width: 500px; margin-top: 50px; text-align: center;">
			<input type="hidden" name="idform" value="<?php echo $row['id'] ?>">
			<div style="padding-bottom:20px;" class="text-right">
				<a class="btn btn-secondary" href="<?php echo $url; ?>"><i class="fas fa-long-arrow-alt-right"></i> رجوع</a>
			</div>
			<p>الاجراء المتخذ لغرض المتابعة <?php echo '<b>'.$row['f3'].'</b>'; ?></p>
			<textarea class="form-control" rows="10" name="msg"><?php echo $row['getaction'] ?></textarea><br/>
			<input type="submit" class="btn btn-primary">
		</form>
		</div>
	<?php
	}
}elseif ($action == 'SendAction') {

	if ($_SERVER['REQUEST_METHOD'] == 'POST') {

			// Get Variables From The Form
			$idform   	    = $_POST['idform'];
			$msg   			= $_POST['msg'];

			$stm = $db->prepare("UPDATE identities SET getaction = ? WHERE id = ?");
			$stm->execute(array($msg, $idform));

			// Echo Success Message

			$theMsg = '<div style="padding-top:20px" class="alert-custom"><div class="container text-center alert alert-success"><i class="fas fa-check"></i>
			تم تحديث البيانات بنجاح</div></div>';
			redirectHome($theMsg);
	}

}elseif ($action == 'UpdateCategory') {

	if ($_SERVER['REQUEST_METHOD'] == 'POST') {

			// Get Variables From The Form
			$idform   	    = $_POST['idform'];
			$statusform   	= $_POST['statusform'];

			$stm = $db->prepare("UPDATE identities SET category = ? WHERE id = ?");
			$stm->execute(array($statusform, $idform));

			// Echo Success Message

			$theMsg = '<div style="padding-top:20px" class="alert-custom"><div class="container text-center alert alert-success"><i class="fas fa-check"></i>
			تم تحديث البيانات بنجاح</div></div>';
			redirectHome($theMsg);
	}  
} elseif ($action == 'Delete') { // Delete News Page

		// Check if get request id is numeric & get the integer value of it
		$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;

		// Select all data depend on this ID
		$check = checkItem('id', 'identities', $id);

		// If there's such ID show the form
		if ($check > 0) { 
			
			$stm = $db->prepare("DELETE FROM identities WHERE id = :zform");
			$stm->bindParam(":zform", $id);
			$stm->execute();

			$theMsg = '<div style="padding-top:20px" class="alert-custom "><div class="container text-center alert alert-success"><i class="fas fa-check"></i> تم الحذف بنجاح</div></div>';
			redirectHome($theMsg, 'back', 1);

		} else {
			$theMsg = '<div style="padding-top:20px" class="alert-custom "><div class="container form-custom text-center alert alert-danger"><i class="fas fa-exclamation"></i> خطأ: المعرف غير موجود</div></div>';
			redirectHome($theMsg);
		}

}

require "view/footer.php";

} else {

	header("Location:./index.php");
	exit();
}
ob_end_flush(); // Output Buffering End
?>
