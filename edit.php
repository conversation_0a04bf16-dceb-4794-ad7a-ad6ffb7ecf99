<?php
ob_start();
session_start();
if (isset($_SESSION['LoginToAdminIdentity'])) {
    if ($_SESSION['LoginToAdminIdentityID'] != 18) {
        require '../init.php';
        // Check if get request id is numeric & get the integer value of it
        $id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
        // Select all data depend on this ID
        $query = 'SELECT * FROM identityalmkhtar WHERE id = ? LIMIT 1';
        $row = $db->Fetch($query, [$id]);
        // The row count
        $count = $db->RowCountData($query, [$id]);

        $url = isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ? $_SERVER['HTTP_REFERER'] : 'index.php';
        // If there's such ID show the form
        if ($count > 0) {
            $db->Update("Update identityalmkhtar set `readable` = :readable where id = :id", [
                'id' => $id,
                'readable' => '1'
            ]);
?>
            <!doctype html>
            <html lang="ar" dir="rtl">

            <head>
                <!-- Required meta tags -->
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

                <!-- Bootstrap CSS -->
                <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
                <link rel="stylesheet" href="all.min.css">
                <link rel="stylesheet" href="cropper.min.css">
                <link rel="stylesheet" href="style.css">
                <title><?php echo $row['fullname'] ?></title>
            </head>

            <body>
                <div class="modal fade" id="CropModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="CropModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h1 class="modal-title fs-5" id="CropModalLabel">اقتصاص الصورة</h1>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="cropimg" style="direction: ltr;">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div style="min-height: 497px;max-height: 497px">
                                                <img src="" id="sample_image" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="preview"></div>
                                            <div class="btn-group mt-5" style="display: inherit!important;">
                                                <button type="button" class="btn btn-primary" title="Zoom In" id="Zoom_In">
                                                    <span>
                                                        <span class="fa fa-search-plus"></span>
                                                    </span>
                                                </button>
                                                <button type="button" class="btn btn-primary" title="Zoom Out" id="Zoom_Out">
                                                    <span>
                                                        <span class="fa fa-search-minus"></span>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" id="crop">اقتصاص</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container text-right">
                    <div class="text-right">
                        <a class="btn btn-secondary" href="<?php echo $url; ?>"><i class="fas fa-long-arrow-alt-right"></i> رجوع</a>
                    </div>
                    <h4 class="text-center mt-2">تعديل استمارة <?php echo '<b>' . $row['fullname'] . '</b>' ?></h4>
                    <hr>
                    <br />
                    <form action="update.php" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="getid" value="<?php echo $id ?>">
                        <div class="col-sm-12 d-flex flex-column align-items-center mb-2">
                            <label for="upload_image" class="btn btn-outline-secondary mb-2">الصورة الشخصية</label>
                            <input type="file" class="d-none" id="upload_image" data-type="upload1">
                            <input type="hidden" id="upload1" name="photo">
                            <input type="hidden" name="oldphoto" value="<?php echo $row['photo']; ?>">
                            <img style="width: 150px!important;" id="uploaded_image" src="https://www.kik.gov.iq/public/storage/<?php echo $row['photo'] ?>">
                        </div>
                        <div class="modal-body text-right">
                            <div class="form-group">
                                <label for="Input0">رقم الاستمارة</label>
                                <input type="text" class="form-control" readonly value="<?php echo $id ?>">
                            </div>
                            <div class="form-group">
                                <label for="Input1">الاسم الرباعي</label>
                                <input type="text" class="form-control" required value="<?php echo $row['fullname'] ?>" name="fullname">
                            </div>
                            <div class="form-group">
                                <label for="Input2">محل وتاريخ الولادة</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" required value="<?php echo $row['goverment'] ?>" name="goverment">
                                    </div>
                                    <div class="col-md-8">
                                        <input type="date" class="form-control" required name="databirth" value="<?php echo $row['date_of_birth'] ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="Input3">فصيلة الدم</label>
                                <input type="text" class="form-control" name="bloadtype" required value="<?php echo $row['blood_type'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="Input4">رقم الهاتف</label>
                                <input type="number" class="form-control" name="phonenumber" required value="<?php echo $row['phone_number'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="Input5">القضاء</label>
                                <input type="text" class="form-control" name="elimination" required value="<?php echo $row['elimination'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="Input55">الناحية</label>
                                <input type="text" class="form-control" name="side" required value="<?php echo $row['side'] ?>">
                            </div>
                            <div class="row g-3">
                                <div class="col-sm-4">
                                    <label for="Input555">القرية او المحلة</label>
                                    <select name="prefixvillage" class="form-control">
                                        <option value="">
                                            اختر
                                        </option>
                                        <option value="قرية" <?php echo $row['prefixvillage'] == 'قرية' ? 'selected' : 0 ?>>
                                            قرية
                                        </option>
                                        <option value="حي" <?php echo $row['prefixvillage'] == 'حي' ? 'selected' : 0 ?>>
                                            حي
                                        </option>
                                        <option value="محلة" <?php echo $row['prefixvillage'] == 'محلة' ? 'selected' : 0 ?>>
                                            محلة
                                        </option>
                                    </select>
                                </div>
                                <div class="col-sm-8">
                                    <label for="Input555">اسم القرية او الحي او المحلة</label>
                                    <input type="text" class="form-control" name="village" required value="<?php echo $row['village'] ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="Input6">رمز ورقم الهوية</label>
                                <input type="text" class="form-control" name="numberidentity" required value="<?php echo $row['code_number_identity'] ?>">
                            </div>
                            <div class="form-group">
                                <label for="Input7">رمز ورقم الختم</label>
                                <input type="text" class="form-control" name="numberstamp" required value="<?php echo $row['number_of_stamp'] ?>">
                            </div>
                        </div>
                        <div class="text-center">
                            <input type="submit" class="btn btn-primary" value="تحديث">
                        </div>
                    </form>
                    <br>
                </div>
                <!-- End Download Upload Corel Modal -->
                <!-- jQuery first, then Popper.js, then Bootstrap JS -->
                <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
                <script src="all.min.js"></script>
                <script src="cropper.min.js"></script>
                <script src="style.js"></script>
                <script>
                    $(document).ready(function() {
                        var $modal = $('#CropModal');

                        var image = document.getElementById('sample_image');

                        var cropper;

                        $('#upload_image').change(function(event) {
                            var files = event.target.files;
                            var thisinput = $(this);
                            var done = function(url) {
                                image.src = url;
                                $modal.modal('show');
                                $("#crop").attr('data-upload-image', thisinput.attr('data-type'));
                            };

                            if (files && files.length > 0) {
                                reader = new FileReader();
                                reader.onload = function(event) {
                                    done(reader.result);
                                };
                                reader.readAsDataURL(files[0]);
                            }
                        });

                        $modal.on('shown.bs.modal', function() {
                            cropper = new Cropper(image, {
                                dragMode: 'move',
                                aspectRatio: 2.5 / 3.0,
                                autoCropArea: 0.65,
                                restore: false,
                                guides: false,
                                center: false,
                                highlight: false,
                                cropBoxMovable: false,
                                cropBoxResizable: false,
                                toggleDragModeOnDblclick: false,
                            });
                        }).on('hidden.bs.modal', function() {
                            cropper.destroy();
                            cropper = null;
                        });

                        $('#crop').click(function() {
                            var dataUploadImage = $(this).attr('data-upload-image');
                            canvas = cropper.getCroppedCanvas({
                                width: 400,
                                height: 600
                            });
                            canvas.toBlob(function(blob) {
                                url = URL.createObjectURL(blob);
                                var reader = new FileReader();
                                reader.readAsDataURL(blob);
                                reader.onloadend = function() {
                                    var base64data = reader.result;
                                    $('#uploaded_image').attr('src', base64data);
                                    $("#upload1").val(base64data);

                                    $modal.modal('hide');
                                };
                            });
                        });
                        $('#Zoom_In').click(function() {
                            cropper.zoom(0.1);
                        });
                        $('#Zoom_Out').click(function() {
                            cropper.zoom(-0.1);
                        });
                    });
                </script>
            </body>

            </html>
<?php
        } else {
            header('Location:dashboard.php');
            exit();
        }
    } else {
        header('Location:dashboard.php');
        exit();
    }
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
