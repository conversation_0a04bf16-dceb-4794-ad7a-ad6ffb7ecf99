<?php
if (!empty($_FILES)) {
	if (is_uploaded_file($_FILES['uploadFile']['tmp_name'])) {
		sleep(1);
		 require '../../../database/database.class.php';
		$id = $_POST['id'];

		$getstatus = $db->Update("UPDATE identityalmkhtar SET status = 'قيد الانجاز' WHERE id = ? AND status = 'تجديد'", [$id]);
		$db->Update("Update identityalmkhtar set `corel` = :corel where id = :id", [
			'id' => $id,
			'corel' => $_FILES['uploadFile']['name']
		]);
		$source_path = $_FILES['uploadFile']['tmp_name'];
		$target_path = '../uploads/corel/' . $_FILES['uploadFile']['name'];
		move_uploaded_file($source_path, $target_path);
	}
} else {
	header('Location:../index.php');
	exit();
}
