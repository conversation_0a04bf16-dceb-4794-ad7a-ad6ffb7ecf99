<?php
ob_start();
session_start();
if (isset($_SESSION['LoginToAdminIdentity'])) {
    if ($_SESSION['LoginToAdminIdentity'] == 'w.site') {
        require '../../../database/database.class.php';
        $id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
        $row = $db->Fetch("SELECT * FROM identityalmkhtar WHERE id = ?", [$id]);
?>
        <!DOCTYPE html>
        <html>

        <head>
            <title><?php echo $row['fullname'] ?></title>
            <link href="css/bootstrap.min.css" rel="stylesheet" />
            <script src="js/jquery-1.10.2.min.js"></script>
            <script src="js/bootstrap.min.js"></script>
            <script src="js/jquery.form.js"></script>
            <script>
                var input = document.getElementById('uploadSubmit');
                input.onchange = e => {

                    // getting a hold of the file reference
                    var file = e.target.files[0];

                    // setting up the reader
                    var reader = new FileReader();
                    reader.readAsText(file, 'UTF-8');

                    // here we tell the reader what to do when it's done reading...
                    reader.onload = readerEvent => {
                        var content = readerEvent.target.result; // this is the content!
                        console.log(content);
                    }

                }

                input.click();
            </script>
        </head>

        <body>
            <div class="container">
                <br />
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="text-center"><b><?php echo $row['fullname'] ?></b></h3>
                    </div>
                    <div class="panel-body">
                        <form id="uploadImage" action="upload.php" method="post">
                            <input type="hidden" name="id" value="<?php echo $id ?>">
                            <div class="form-group">
                                <input type="file" name="uploadFile" id="uploadFile" />
                            </div>
                            <div class="form-group">
                                <input type="submit" id="uploadSubmit" upload="asasas" value="Upload" class="btn btn-info" />
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </form>
                        <div id="successUpload" class="alert alert-success text-center" style="display:none;">تم تحميل الملف
                            بنجاح</div>
                    </div>
                </div>
            </div>
        </body>

        </html>

        <script>
            $(document).ready(function() {
                $('#uploadImage').submit(function(event) {
                    if ($('#uploadFile').val()) {
                        event.preventDefault();
                        $('#successUpload').hide();
                        $(this).ajaxSubmit({
                            beforeSubmit: function() {
                                $('.progress-bar').width('50%');
                            },
                            uploadProgress: function(event, position, total, percentageComplete) {
                                $("#uploadSubmit").val(percentageComplete + '%');
                                $('.progress-bar').animate({
                                    width: percentageComplete + '%'
                                }, {
                                    duration: 1000
                                });
                            },
                            success: function() {
                                $('#successUpload').show();
                                $("#uploadSubmit").val('upload');
                            },
                            resetForm: true
                        });
                    }
                    return false;
                });
            });
        </script>
<?php
    } else {
        header('Location:../index.php');
        exit();
    }
} else {
    header('Location:../index.php');
    exit();
}
ob_end_flush();