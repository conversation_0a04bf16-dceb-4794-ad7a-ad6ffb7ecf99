<?php
require '../init.php';
require '../vendor/autoload.php';
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id                  = $_POST['getid'];
    $fullname            = $_POST['fullname'];
    $goverment           = $_POST['goverment'];
    $databirth           = $_POST['databirth'];
    $bloadtype           = $_POST['bloadtype'];
    $phonenumber         = $_POST['phonenumber'];
    $elimination         = $_POST['elimination'];
    $side                = $_POST['side'];
    $village             = $_POST['village'];
    $prefixvillage       = $_POST['prefixvillage'];
    $numberidentity      = $_POST['numberidentity'];
    $numberstamp         = $_POST['numberstamp'];

    $oldphoto                = $_POST['oldphoto'];

    if (!empty($fullname) && !empty($goverment) && !empty($databirth) && !empty($bloadtype) && !empty($phonenumber) && !empty($elimination) && !empty($side) && !empty($village) && !empty($numberidentity) && !empty($numberstamp)) {

        $f2Name = $_POST['photo'];
        if (!empty($f2Name)) {



            $image_array_1 = explode(";", $f2Name);
            $image_array_2 = explode(",", $image_array_1[1]);
            $f2Name = base64_decode($image_array_2[1]);

            $f2Upload = rand(0, 10000000) . '_' . time() . '.png';

            $f2 = 'uploads/identityalmkhtar/' . $f2Upload;

            file_put_contents("../../public_html/public/storage/" . $f2, $f2Name);

            $client = new GuzzleHttp\Client();
            $res = $client->post('https://api.remove.bg/v1.0/removebg', [
                'multipart' => [
                    [
                        'name'     => 'image_file',
                        'contents' => fopen('../../public_html/public/storage/' . $f2, 'r')
                    ],
                    [
                        'name'     => 'size',
                        'contents' => 'auto'
                    ]
                ],
                'headers' => [
                    'X-Api-Key' => 'uJwgfy1zCeatcdguLzPC1iXm'
                ]
            ]);

            $fp = fopen("../../public_html/public/storage/" . $f2, "wb");
            fwrite($fp, $res->getBody());
            fclose($fp);
        } else {
            $f2 = $oldphoto;
        }

        $db->Update(
            "UPDATE identityalmkhtar SET 
        `fullname`              = :fullname, 
        `code_number_identity`  = :code_number_identity, 
        `date_of_birth`         = :date_of_birth,
        `goverment`             = :goverment,
        `blood_type`            = :blood_type,
        `elimination`           = :elimination,
        `side`                  = :side,
        `village`               = :village,
        `prefixvillage`         = :prefixvillage,
        `number_of_stamp`       = :number_of_stamp,
        `phone_number`          = :phone_number,
        `photo`                 = :photo,
        `updated_at`            = now()
        WHERE id                = :id",
            [
                'id'                         => $id,
                'fullname'                   => $fullname,
                'code_number_identity'       => $numberidentity,
                'date_of_birth'              => $databirth,
                'goverment'                  => $goverment,
                'blood_type'                 => $bloadtype,
                'elimination'                => $elimination,
                'side'                       => $side,
                'village'                    => $village,
                'prefixvillage'              => $prefixvillage,
                'number_of_stamp'            => $numberstamp,
                'phone_number'               => $phonenumber,
                'photo'                      => $f2,
            ]
        );
        header('Location:dashboard.php');
        exit();
    }
} else {
    header('Location:index.php');
    exit();
}
