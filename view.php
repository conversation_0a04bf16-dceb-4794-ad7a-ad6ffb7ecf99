<?php
require '../init.php';
include 'phpqrcode/qrlib.php';
$id = $_POST['id'];
if (isset($id)) {
    $output = array();
    $result = $db->FetchAll("SELECT * FROM identityalmkhtar WHERE id = ? LIMIT 1", [$id]);
    $db->Update("Update identityalmkhtar set `readable` = 1 where id = ?", [$id]);
    foreach ($result as $row) {
        $output["id"]                                   = $row["id"];
        $output["fullname"]                             = $row["fullname"];
        $output["code_number_identity"]                 = $row["code_number_identity"];
        $output["date_of_birth"]                        = $row["date_of_birth"];
        $output["goverment"]                            = $row["goverment"];
        $output["blood_type"]                           = $row["blood_type"];
        $output["elimination"]                          = $row["elimination"];
        $output["side"]                                 = $row["side"];
        $output["village"]                              = $row["village"];
        $output["prefixvillage"]                        = $row["prefixvillage"];
        $output["number_of_stamp"]                      = $row["number_of_stamp"];
        $output["phone_number"]                         = $row["phone_number"];
        $output["expiration_date"]                      = $row["expiration_date"];
        $output["photo"]                                = $row["photo"];
        $output["pdf"]                                  = $row["pdf"];
        $output["corel"]                                = $row["corel"];
        $output["status"]                               = $row["status"];
        $output["created_at"]                           = $row["created_at"];

        $contents = 'https://id.kik.gov.iq/auth-qr-almkhtar/index.php?a=' . $row["id"];
        $imagename = 'qrcode' . md5($row['id']) . '.png';
        $filename = 'qcodeimg/' . $imagename;
        if (!empty($row['fingerprint'])) {
            $fingerprint = 'uploads/fingerprint/' . $row['fingerprint'] . '';
        } else {
            $fingerprint = 'null';
        }


        // $ecc stores error correction capability('L') 
        $ecc = 'L';
        $pixel_Size = 3;
        $frame_Size = 3;

        // Generates QR Code and Stores it in directory given 
        QRcode::png($contents, $filename, $ecc, $pixel_Size, $frame_Size);

        $output["qrcode"]                           = $filename;
        $output["fingerprint"]                      = $fingerprint;
    }
    echo json_encode($output);
}
