<?php
require '../init.php';
if (isset($_POST['downloadpdf'])) {
    $id             = $_POST['getpdfid'];
    $filename       = $_FILES['file']['name'];
    $filesize       = $_FILES['file']['size'];
    $filetmp        = $_FILES['file']['tmp_name'];

    $extension = explode('.', $filename);

    $output = array();

    $checkExtension = array('pdf', 'jpg', 'jpeg');

    $pdfFinal = md5($filename) . rand(1111, 9999) . '.' . end($extension);
    if (empty($filename)) {
        $output['error'] = 'يرجى رفع ملف';
        $output['result'] = false;
    } elseif ($filesize > 500000) {
        $output['error'] = 'يرجى رفع ملف اقل من 500 كيلوبايت';
        $output['result'] = false;
    } elseif (!in_array(end($extension), $checkExtension)) {
        $output['error'] = 'الملف المسموح به PDF فقط';
        $output['result'] = false;
    } else {
        move_uploaded_file($filetmp, 'uploads/pdf/' . $pdfFinal);
        $db->Update("Update identityalmkhtar set `pdf` = :pdf where id = :id", [
            'id' => $id,
            'pdf' => $pdfFinal
        ]);
        $output['id'] = $id;
        $output['pdfFinal'] = $pdfFinal;
        $output['result'] = true;
    }
    echo json_encode($output);
}

if (isset($_POST['downloadcorel'])) {
    $id             = $_POST['getcorelid'];
    $filename       = $_FILES['file']['name'];
    $filesize       = $_FILES['file']['size'];
    $filetmp        = $_FILES['file']['tmp_name'];

    $extension = explode('.', $filename);

    $output = array();

    $checkExtension = array('cdr');

    $corelFinal = md5($filename) . rand(1111, 9999) . '.' . end($extension);
    if (empty($filename)) {
        $output['error'] = 'يرجى رفع ملف';
        $output['result'] = false;
    } elseif (!in_array(end($extension), $checkExtension)) {
        $output['error'] = 'الملف المسموح به cdr فقط';
        $output['result'] = false;
    } else {
        move_uploaded_file($filetmp, 'uploads/corel/' . $corelFinal);
        $db->Update("Update identityalmkhtar set `corel` = :corel where id = :id", [
            'id' => $id,
            'corel' => $corelFinal
        ]);
        $output['id'] = $id;
        $output['pdfFinal'] = $corelFinal;
        $output['result'] = true;
    }
    echo json_encode($output);
}
