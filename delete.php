<?php
require '../init.php';

if (isset($_POST['id'])) {
    $id         = $_POST['id'];
    $db->Remove("Delete from identityalmkhtar where id = :id", [
        'id' => $id
    ]);
    echo 'تم حذف الاستمارة بنجاح';
} else {
    echo 'خطأ في حذف الاستمارة!';
}

if (isset($_POST['deletepdf'])) {
    $deletepdf  = $_POST['deletepdf'];
    $row = $db->Fetch('SELECT * FROM identityalmkhtar WHERE id = ?', [$deletepdf]);
    unlink('uploads/pdf/' . $row['pdf']);
    $db->Update("Update identityalmkhtar set `pdf` = :pdf where id = :id", [
        'id' => $deletepdf,
        'pdf' => ''
    ]);
    echo 'تم الحذف بنجاح';
} else {
    echo 'خطأ في الحذف !';
}

if (isset($_POST['deletecorel'])) {
    $deletecorel  = $_POST['deletecorel'];
    $row = $db->Fetch('SELECT * FROM identityalmkhtar WHERE id = ?', [$deletecorel]);
    unlink('uploads/corel/' . $row['corel']);
    $db->Update("Update identityalmkhtar set `corel` = :corel where id = :id", [
        'id' => $deletecorel,
        'corel' => ''
    ]);
    echo 'تم الحذف بنجاح';
} else {
    echo 'خطأ في الحذف !';
}
