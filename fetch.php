<?php
ob_start();
session_start();
if (isset($_SESSION['LoginToAdminIdentity'])) {
?>
    <script>
        $(function() {
            'use strict';

            /* *******************************************
             *********************************************
             *********************************************
             ************** Copy Text  *******************
             *********************************************
             *********************************************
             *********************************************/

            copyText('#copyf3', '.copyfullname');
            copyText('#copyf8', '.copyelimination');
            copyText('#copyf888', '.copyprefixvillage');
            copyText('#copyf9', '.copycode_number_identity');
            copyText('#copyf10', '.copynumber_of_stamp');

            /* *******************************************
             *********************************************
             *********************************************
             ************* Date Format *******************
             *********************************************
             *********************************************
             *********************************************/
            $.date = function(dateObject) {
                var d = new Date(dateObject);
                var day = d.getDate();
                var month = d.getMonth() + 1;
                var year = d.getFullYear();
                if (day < 10) {
                    day = "0" + day;
                }
                if (month < 10) {
                    month = "0" + month;
                }
                var date = day + "-" + month + "-" + year;

                return date;
            };
            /* *******************************************
             *********************************************
             *********************************************
             ************* Delete Record Ajax ************
             *********************************************
             *********************************************
             *********************************************/
            $(".delete").on('click', function() {
                var message = '',
                    confirmDelete = confirm('هل انت متأكد!'),
                    id = $(this).attr('id'),
                    removeEl = $(this);
                if (confirmDelete == true) {
                    $.ajax({
                        method: 'POST',
                        url: 'delete.php',
                        data: {
                            id: id
                        },
                        beforeSend: function() {
                            $('body').css('background', 'url("reload.gif")');
                            $('body').css('background-repeat', 'no-repeat');
                            $('body').css('background-position', 'center');
                            $('body').css('opacity', '0.1');
                            $('a').attr('disabled', 'true');
                            $('button').attr('disabled', 'true');
                        },
                        success: function(data) {
                            $(removeEl).closest('tr').fadeOut(800, function() {
                                $(this).remove();
                            });
                        },
                        complete: function() {
                            $('body').css('background', 'none');
                            $('body').css('background-repeat', 'none');
                            $('body').css('background-position', 'none');
                            $('body').css('opacity', '1');
                            $('a').removeAttr('disabled', 'false');
                            $('button').removeAttr('disabled', 'false');
                        }
                    });
                }
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* View Record Ajax **************
             *********************************************
             *********************************************
             *********************************************/
            $(".view").on('click', function() {
                var id = $(this).attr('id');
                $.ajax({
                    url: 'view.php',
                    method: 'POST',
                    dataType: "json",
                    data: {
                        id: id
                    },
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {

                        $("#viewFormModalLong").modal('show');
                        $('.f0 img').attr("src", 'https://kik.gov.iq/public/storage/' + data.photo);
                        $('.f1').html('رقم الاستمارة: <b>' + id + '</b>');
                        $('.f2').html('الاجراء: <b>' + data.status + '</b>');
                        $('.f3').html('الاسم الرباعي: <span class="copyfullname"><b>' + data
                            .fullname +
                            '</b></span>');
                        $('.f4').html('محل الولادة: <b>' + data.goverment + '</b>');
                        $('.f5').html('تاريخ الولادة: <b>' + $.date(data.date_of_birth) + '</b>');
                        $('.f6').html('فصيلة الدم : <b>' + data.blood_type + '</b>');
                        $('.f7').html('رقم الهاتف : <b>' + data.phone_number + '</b>');
                        $('.f8').html('<span class="copyelimination"><b>' + data.elimination + ' ' +
                            data
                            .side + '</b></span>');
                        $('.f888').html('<span class="copyprefixvillage"><b>' + data.prefixvillage +
                            ' ' +
                            data.village + '</b></span>');
                        $('.f9').html(
                            'رمز ورقم الهوية : <span class="copycode_number_identity"><b>' +
                            data.code_number_identity + '</b></span>');
                        $('.f10').html('رمز ورقم الختم : <span class="copynumber_of_stamp"><b>' +
                            data
                            .number_of_stamp + '</b></span>');
                        $('.f11').html('تاريخ الاصدار : <b>' + $.date(data.created_at) + '</b>');
                        $('.f12').html('تاريخ النفاذ : <b>' + $.date(data.expiration_date) +
                            '</b>');
                        $('.qrcode img').attr('src', data.qrcode);
                        $('.fingerprint img').attr('src', data.fingerprint);

                        $("#readable" + id).removeAttr('class');
                        if (data.file != null) {
                            var getfile =
                                '<a style="margin-top: 25px;" target="_blank" href="../../public/storage/' +
                                data.file + '" class="btn btn-info">تحميل الملف</a>';
                        } else {
                            var getfile = '<p><b>لايوجد ملف</b></p>';
                        }
                        $('#printframe').attr('src', 'print.php?id=' + id);
                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* Fetch Status Form Ajax ********
             *********************************************
             *********************************************
             *********************************************/
            $(".changestatus").on('click', function() {
                var id = $(this).attr("id");
                $.ajax({
                    url: 'view.php',
                    method: 'POST',
                    dataType: "json",
                    data: {
                        id: id
                    },
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {
                        $("#ChangeStatusModalCenter").modal('show');
                        $("#getfullname").text(data.fullname);
                        $("#getidstatus").val(data.id);
                        $("#statusSelect").val(data.status);
                        $("#readable" + data.id).removeAttr('class');
                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* Update Status Form Ajax *******
             *********************************************
             *********************************************
             *********************************************/
            $("#ChangeStatusForm").on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                console.log(formData);
                $.ajax({
                    method: 'POST',
                    url: 'changestatus.php',
                    data: formData,
                    dataType: "json",
                    contentType: false,
                    cache: false,
                    processData: false,
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {
                        localStorage.setItem('status', data.status);
                        localStorage.setItem('id', data.id);
                        var setstatus = $(".setstatus" + localStorage.getItem('id'));
                        setstatus.parent().css('background', '#26982f');
                        $("#ChangeStatusModalCenter").modal('hide');
                        if (localStorage.getItem('status') == "تجديد") {
                            $(".corelimg" + data.id).html(
                                '<i style="color:#8a8484" class="fas fa-cloud fa-2x customColorCorel' +
                                data.id + '"></i>');
                        }
                        if (localStorage.getItem('status') == 'قيد المراجعة') {
                            var getStatus = 'قيد المراجعة';
                        } else if (data.status == 'قيد الانجاز') {
                            var getStatus =
                                '<i style="color:#ffc107" class="fas fa-hourglass-end fa-2x"></i>';
                        } else if (data.status == 'انجزت') {
                            var getStatus =
                                '<i style="color:#28a745" class="fas fa-check-square fa-2x"></i>';
                        } else if (data.status == 'تجديد') {
                            var getStatus =
                                '<i style="color:#17a2b8" class="fas fa-sync-alt fa-2x"></i>';
                        }
                        setstatus.html(getStatus);
                        setstatus.parent().css({
                            'background': 'none',
                            'transition': 'all 2s ease-in-out'
                        });
                        setTimeout(function() {
                            setstatus.parent().removeAttr('style');
                        }, 3000);

                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* Download & Upload Pdf *********
             *********************************************
             *********************************************
             *********************************************/
            $(".pdf").on('click', function() {
                var id = $(this).attr('id');
                $.ajax({
                    url: 'view.php',
                    method: 'POST',
                    dataType: "json",
                    data: {
                        id: id
                    },
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {
                        $("#DownloadUploadPdfModalCenter").modal('show');
                        $("#deletepdf").attr('data-id', id);
                        $('#getpdfid').val(id);
                        $("#getpdffullname").text(data.fullname);
                        $("#readable" + id).removeAttr('class');
                        if (Boolean(data.pdf) == true) {
                            $("#pdfIcon").html('<a style="color:red" href="uploads/pdf/' + data.pdf +
                                '"><i class="fas fa-file-pdf fa-3x"></i></a>');
                            $("#deletepdf").css('display', 'block');
                        } else {
                            $("#deletepdf").css('display', 'none');
                            $("#pdfIcon").html('<h3>لايوجد ملف</h3>');
                        }
                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* Submit Pdf To Database ********
             *********************************************
             *********************************************
             *********************************************/
            $("#DownloadUploadPdf").on('submit', function(e) {
                e.preventDefault();
                var formdata = new FormData(this);
                formdata.append('downloadpdf', 'downloadpdf');
                $.ajax({
                    method: 'POST',
                    url: 'upload.php',
                    data: formdata,
                    dataType: 'json',
                    contentType: false,
                    cache: false,
                    processData: false,
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {
                        if (data.result == true) {
                            $("#DownloadUploadPdfModalCenter").modal('hide');
                            $(".customColor" + data.id).css('color', 'red');
                            $("#file").val('');
                        } else {
                            alert(data.error);
                        }


                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             ***  Delete Pdf From Database & Directory ***
             *********************************************
             *********************************************
             *********************************************/
            $("#deletepdf").on("click", function(e) {
                e.stopImmediatePropagation();
                var id = $(this).attr('data-id');
                var confirmDelete = confirm('هل انت متأكد');
                if (confirmDelete == true) {
                    $.ajax({
                        method: 'POST',
                        url: 'delete.php',
                        data: {
                            deletepdf: id
                        },
                        beforeSend: function() {
                            $('body').css('background', 'url("reload.gif")');
                            $('body').css('background-repeat', 'no-repeat');
                            $('body').css('background-position', 'center');
                            $('body').css('opacity', '0.1');
                            $('a').attr('disabled', 'true');
                            $('button').attr('disabled', 'true');
                        },
                        success: function(data) {
                            if (Boolean(data) == true) {
                                $("#DownloadUploadPdfModalCenter").modal('hide');
                                $(".customColor" + id).css('color', '#8a8484');
                            }
                        },
                        complete: function() {
                            $('body').css('background', 'none');
                            $('body').css('background-repeat', 'none');
                            $('body').css('background-position', 'none');
                            $('body').css('opacity', '1');
                            $('a').removeAttr('disabled', 'false');
                            $('button').removeAttr('disabled', 'false');
                        }
                    });
                }
            });
            /* *******************************************
             *********************************************
             *********************************************
             ************* Download & Upload Pdf *********
             *********************************************
             *********************************************
             *********************************************/
            $(".corel").on('click', function() {
                var id = $(this).attr('id');
                $.ajax({
                    url: 'view.php',
                    method: 'POST',
                    dataType: "json",
                    data: {
                        id: id
                    },
                    beforeSend: function() {
                        $('body').css('background', 'url("reload.gif")');
                        $('body').css('background-repeat', 'no-repeat');
                        $('body').css('background-position', 'center');
                        $('body').css('opacity', '0.1');
                        $('a').attr('disabled', 'true');
                        $('button').attr('disabled', 'true');
                    },
                    success: function(data) {
                        $("#DownloadUploadCorelModalCenter").modal('show');
                        $("#deletecorel").attr('data-id', id);
                        $('#getcorelid').val(id);
                        $("#getcorelfullname").text(data.fullname);
                        $('#btnDownloadCorel').attr('href', 'uploadfile/index.php?id=' + id);
                        $("#readable" + id).removeAttr('class');
                        if (Boolean(data.corel) == true) {
                            $("#corelIcon").html('<a style="color:#339af0" href="uploads/corel/' +
                                data.corel +
                                '"><i class="fas fa-cloud-download-alt fa-3x"></i></a>');
                            $("#deletecorel").css('display', 'block');
                        } else {
                            $("#deletecorel").css('display', 'none');
                            $("#corelIcon").html('<h3>لايوجد ملف</h3>');
                        }
                    },
                    complete: function() {
                        $('body').css('background', 'none');
                        $('body').css('background-repeat', 'none');
                        $('body').css('background-position', 'none');
                        $('body').css('opacity', '1');
                        $('a').removeAttr('disabled', 'false');
                        $('button').removeAttr('disabled', 'false');
                    }
                });
            });
            /* *******************************************
             *********************************************
             *********************************************
             *** Delete Corel From Database &Directory ***
             *********************************************
             *********************************************
             *********************************************/
            $("#deletecorel").on("click", function(e) {
                e.stopImmediatePropagation();
                var id = $(this).attr('data-id');
                var confirmDelete = confirm('هل انت متأكد');
                if (confirmDelete == true) {
                    $.ajax({
                        method: 'POST',
                        url: 'delete.php',
                        data: {
                            deletecorel: id
                        },
                        beforeSend: function() {
                            $('body').css('background', 'url("reload.gif")');
                            $('body').css('background-repeat', 'no-repeat');
                            $('body').css('background-position', 'center');
                            $('body').css('opacity', '0.1');
                            $('a').attr('disabled', 'true');
                            $('button').attr('disabled', 'true');
                        },
                        success: function(data) {
                            if (Boolean(data) == true) {
                                $("#DownloadUploadCorelModalCenter").modal('hide');
                                $(".corelimg" + id).html(
                                    '<i style="color:#8a8484" class="fas fa-cloud fa-2x customColorCorel' +
                                    id + '"></i>');
                            }
                        },
                        complete: function() {
                            $('body').css('background', 'none');
                            $('body').css('background-repeat', 'none');
                            $('body').css('background-position', 'none');
                            $('body').css('opacity', '1');
                            $('a').removeAttr('disabled', 'false');
                            $('button').removeAttr('disabled', 'false');
                        }
                    });
                }
            });
        });
        // Copy Function
        function copy(selector) {
            var $temp = $("<div>");
            $("body").append($temp);
            $temp.css('position', 'fixed');
            $temp.attr("contenteditable", true)
                .html($(selector).text()).select()
                .on("focus", function() {
                    document.execCommand('selectAll', false, null);
                })
                .focus();
            document.execCommand("copy");
            $temp.remove();
        }

        function copyText(element1, element2) {

            $(element1).on('click', function(e) {
                e.preventDefault();
                copy(element2);
                $('#msgCopy').css('display', 'block');
                setInterval(msgCopy, 1000);
            });
            let msgCopy = () => {
                $('#msgCopy').css('display', 'none');
            };
        }
    </script>
<?php

    require '../init.php';

    $limit = '50';
    $page = 1;
    if ($_POST['page'] > 1) {
        $start = (($_POST['page'] - 1) * $limit);
        $page = $_POST['page'];
    } else {
        $start = 0;
    }

    $query = "
SELECT * FROM identityalmkhtar 
";
    $queryInput1 = $_POST['query'];
    $queryInput2 = $_POST['query2'];
    $queryInput3 = $_POST['query3'];
    $queryInput4 = $_POST['query4'];
    $queryInput5 = $_POST['query5'];
    $queryInput6 = $_POST['query6'];
    if (!empty($queryInput1) || !empty($queryInput2) || !empty($queryInput3) || !empty($queryInput4) || !empty($queryInput5) || !empty($queryInput6)) {
        $query .= '
    WHERE id LIKE "%' . str_replace(' ', '%', $queryInput1) . '%" 
    AND fullname LIKE "%' . str_replace(' ', '%', $queryInput2) . '%"
    AND phone_number LIKE "%' . str_replace(' ', '%', $queryInput3) . '%"
    AND status LIKE "%' . str_replace(' ', '%', $queryInput4) . '%"
    AND code_number_identity LIKE "%' . str_replace(' ', '%', $queryInput5) . '%"
    AND number_of_stamp LIKE "%' . str_replace(' ', '%', $queryInput6) . '%"
  ';
    }

    $query .= 'ORDER BY id DESC ';

    $filter_query = $query . 'LIMIT ' . $start . ', ' . $limit . '';

    $total_data = $db->RowCountData($query);

    $result = $db->FetchAll($filter_query);

    $total_filter_data = $db->RowCountData($filter_query);

    $output = '
<div class="hint-text text-right">عرض <b>' . $limit . '</b> من اصل <b>' . $total_data . '</b> استمارة</div>
<table class="table table-bordered table-hover">
          <thead class="thead-dark">
            <tr>
              <th scope="col">رقم الاستمارة</th>
              <th scope="col">التاريخ</th>
              <th scope="col">الاسم الرباعي</th>
              <th scope="col" data-toggle="modal" data-target="#CountStatusModal" style="cursor: pointer;">الاجراء</th>
              <th scope="col">رقم الهاتف</th>
              <th scope="col">رمز ورقم الهوية</th>
              <th scope="col">رمز ورقم الختم</th>
              <th scope="col">الادارة</th>
            </tr>
          </thead><tbody>
';
    if ($total_data > 0) {

        foreach ($result as $form) {
            $output .= '<tr id="readable' . $form['id'] . '" ' . ($form['readable'] == 0 ? 'class="table-danger"' : '') . '>
    <th>' . $form['id'] . '</th>
    <td>' . date('Y-m-d', strtotime($form['created_at'])) . '</td>
    <td id="fullnameTd' . $form['id'] . '">' . $form['fullname'] . '</td>
    <td id="statusTd' . $form['id'] . '"><a class="changestatus setstatus' . $form['id'] . '" id="' . $form['id'] . '"  style="cursor: pointer;">';
            if ($form['status'] == 'قيد المراجعة') {
                $output .= 'قيد المراجعة';
            }
            if ($form['status'] == 'قيد الانجاز') {
                $output .= '<i title="قيد الانجاز" style="color:#ffc107" class="fas fa-hourglass-end fa-2x"></i>';
            }
            if ($form['status'] == 'انجزت') {
                $output .= '<i title="انجزت" style="color:#28a745" class="fas fa-check-square fa-2x"></i>';
            }
            if ($form['status'] == 'تجديد') {
                $output .= '<i title="تجديد" style="color:#17a2b8" class="fas fa-sync-alt fa-2x"></i>';
            }
            $output .= '</a></td>
    <td id="phonenumberTd' . $form['id'] . '">' . $form['phone_number'] . '</td>
    <td id="codenumberidentityTd' . $form['id'] . '">' . $form['code_number_identity'] . '</td>
    <td id="numberofstampTd' . $form['id'] . '">' . $form['number_of_stamp'] . '</td>
    <td>';
            if ($_SESSION['LoginToAdminIdentityID'] == 18) {
                $output .= '<a title="بصمة اصبع" class="btn ' . (!empty($form['fingerprint']) ? 'btn-purple' : 'btn-light') . ' btn-sm ms-1" href="#"><i class="fas fa-fingerprint"></i></a>';
            } else {
                $output .= '<a title="بصمة اصبع" class="btn ' . (!empty($form['fingerprint']) ? 'btn-purple' : 'btn-light') . ' btn-sm ms-1" target="_blank" href="finger.php?id=' . $form['id'] . '" id="' . $form['id'] . '" data-fullname="' . $form['fullname'] . '"><i class="fas fa-fingerprint"></i></a>';
            }
            $output .= '<button title="معاينة" type="button" class="btn btn-success btn-sm view ms-1" id="' . $form['id'] . '"><i class="fas fa-eye"></i></button>';
            if ($_SESSION['LoginToAdminIdentityID'] == 18) {
                $output .= '<button title="حذف" type="button" class="btn btn-danger btn-sm ms-1"><i class="fas fa-trash"></i></button>
                <a title="تعديل" class="btn btn-primary btn-sm ms-1" href="#"><i class="fas fa-edit"></i></a>';
            } else {
                $output .= '<button title="حذف" type="button" class="btn btn-danger btn-sm ms-1 delete" id="' . $form['id'] . '"><i class="fas fa-trash"></i></button>
                <a title="تعديل" class="btn btn-primary btn-sm ms-1" href="edit.php?id=' . $form['id'] . '"><i class="fas fa-edit"></i></a>';
            }

            $output .= '<button title="PDF" type="button" class="btn btn-secondary btn-sm pdf" style="background-color: initial!important; border-color: initial!important;font-size: 14px;" id="' . $form['id'] . '"><i style="' . (!empty($form['pdf']) ? 'color:red' : 'color:#8a8484') . '" class="fas fa-file-pdf fa-2x customColor' . $form['id'] . '"></i></button>
    <button title="Corel Draw" type="button" class="btn btn-secondary btn-sm corel corelimg' . $form['id'] . '" style="background-color: initial!important; border-color: initial!important;font-size: 14px;" id="' . $form['id'] . '">' . (!empty($form['corel']) ? '<img src="coreldraw.png" width="32">' : '<i style="' . (!empty($form['corel']) ? 'color:#339af0' : 'color:#8a8484') . '" class="fas fa-cloud fa-2x customColorCorel' . $form['id'] . '"></i>') . '</button>
    </td>
  </tr>';
        }
    } else {
        $output .= '
  <tr>
    <td colspan="9" align="center"><h5 class="text-center text-danger"><b>عذراً, لايوجد نتائج بحث<b></h5></td>
  </tr>
  ';
    }

    $output .= '
</tbody></table>
<br />';
    $output2 = '<div align="center">
  <ul class="pagination">
';

    $total_links = ceil($total_data / $limit);
    $previous_link = '';
    $next_link = '';
    $page_link = '';

    //echo $total_links;

    if ($total_links > 4) {
        if ($page < 5) {
            for ($count = 1; $count <= 5; $count++) {
                $page_array[] = $count;
            }
            $page_array[] = '...';
            $page_array[] = $total_links;
        } else {
            $end_limit = $total_links - 5;
            if ($page > $end_limit) {
                $page_array[] = 1;
                $page_array[] = '...';
                for ($count = $end_limit; $count <= $total_links; $count++) {
                    $page_array[] = $count;
                }
            } else {
                $page_array[] = 1;
                $page_array[] = '...';
                for ($count = $page - 1; $count <= $page + 1; $count++) {
                    $page_array[] = $count;
                }
                $page_array[] = '...';
                $page_array[] = $total_links;
            }
        }
    } else {
        for ($count = 1; $count <= $total_links; $count++) {
            $page_array[] = $count;
        }
    }
    if (isset($page_array) && count($page_array) > 0) {
        for ($count = 0; $count < count($page_array); $count++) {
            if ($page == $page_array[$count]) {
                $page_link .= '
        <li class="page-item active" aria-current="page">
        <a class="page-link" href="#">' . $page_array[$count] . '</a>
        </li>
        ';

                $previous_id = $page_array[$count] - 1;
                if ($previous_id > 0) {
                    $previous_link = '<li class="page-item"><a class="page-link" href="javascript:void(0)" data-page_number="' . $previous_id . '">السابق</a></li>';
                } else {
                    $previous_link = '
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true" style="color: #777;">السابق</a>
                    </li>
                    ';
                }
                $next_id = $page_array[$count] + 1;
                if ($next_id > $total_links) {
                    $next_link = '
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true" style="color: #777;">التالي</a>

                        </li>';
                } else {
                    $next_link = '<li class="page-item"><a class="page-link" href="javascript:void(0)" data-page_number="' . $next_id . '">التالي</a></li>';
                }
            } else {
                if ($page_array[$count] == '...') {
                    $page_link .= '
        <li class="page-item disabled">
            <a class="page-link" href="#">...</a>
        </li>
        ';
                } else {
                    $page_link .= '
        <li class="page-item"><a class="page-link" href="javascript:void(0)" data-page_number="' . $page_array[$count] . '">' . $page_array[$count] . '</a></li>
        ';
                }
            }
        }
    }
    $output2 .= $previous_link . $page_link . $next_link;
    $output2 .= '
  </ul>

</div>
';
    echo $output;

    echo $output2;
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
