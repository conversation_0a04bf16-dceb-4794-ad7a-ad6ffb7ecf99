<?php

// require 'vendor/autoload.php';
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;


require '../init.php';

$rows = $db->FetchAll("SELECT * FROM identityalmkhtar ORDER BY id DESC");

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

$spreadsheet->getActiveSheet()->getColumnDimension('A')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('B')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('C')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('D')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('E')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('F')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('G')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('H')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('I')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('J')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('K')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('L')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('M')->setAutoSize(true);
$spreadsheet->getActiveSheet()->getColumnDimension('N')->setAutoSize(true);

$spreadsheet->getActiveSheet()->getStyle('A1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('A1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('A1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('B1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('B1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('B1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('C1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('C1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('C1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('D1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('D1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('D1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('E1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('E1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('E1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('F1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('F1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('F1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('G1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('G1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('G1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('H1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('H1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('H1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('I1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('I1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('I1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('J1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('J1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('J1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('K1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('K1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('K1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('L1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('L1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('L1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('M1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('M1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('M1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$spreadsheet->getActiveSheet()->getStyle('N1')
    ->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_WHITE);
$spreadsheet->getActiveSheet()->getStyle('N1')
    ->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
$spreadsheet->getActiveSheet()->getStyle('N1')
    ->getFill()->getStartColor()->setARGB('FFFF0000');

$sheet->setCellValue('A1', 'رقم الاستمارة');

$sheet->setCellValue('B1', 'الاسم الرباعي');

$sheet->setCellValue('C1', 'محل الولادة');

$sheet->setCellValue('D1', 'تاريخ الولادة');

$sheet->setCellValue('E1', 'فصيلة الدم');

$sheet->setCellValue('F1', 'رقم الهاتف');

$sheet->setCellValue('G1', 'القضاء');

$sheet->setCellValue('H1', 'الناحية');

$sheet->setCellValue('I1', 'القرية او المحلة');

$sheet->setCellValue('J1', 'رمز ورقم الهوية');

$sheet->setCellValue('K1', 'رمز ورقم الختم');

$sheet->setCellValue('L1', 'الاجراء');

$sheet->setCellValue('M1', 'تاريخ الاصدار');

$sheet->setCellValue('N1', 'تاريخ النفاذ');

$i = 2;
foreach ($rows as $row) {
    $sheet->setCellValue('A' . $i, $row['id']);
    $sheet->setCellValue('B' . $i, $row['fullname']);
    $sheet->setCellValue('C' . $i, $row['goverment']);
    $sheet->setCellValue('D' . $i, $row['date_of_birth']);
    $sheet->setCellValue('E' . $i, $row['blood_type']);
    $sheet->setCellValue('F' . $i, $row['phone_number']);
    $sheet->setCellValue('G' . $i, $row['elimination']);
    $sheet->setCellValue('H' . $i, $row['side']);
    $sheet->setCellValue('I' . $i, $row['village']);
    $sheet->setCellValue('J' . $i, $row['code_number_identity']);
    $sheet->setCellValue('K' . $i, $row['number_of_stamp']);
    $sheet->setCellValue('L' . $i, $row['status']);
    $sheet->setCellValue('M' . $i, date('Y-m-d', strtotime($row['created_at'])));
    $sheet->setCellValue('N' . $i, date('Y-m-d', strtotime($row['expiration_date'])));
    $i++;
}

$writer = new Xlsx($spreadsheet);
$filename = 'download/excel/families_inventory_' . date('Y-m-d') . '.xlsx';
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="' . $filename);
header('Cache-Control: max-age=0');
$writer->save('php://output');
