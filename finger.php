<?php
ob_start();
session_start();
if (isset($_SESSION['LoginToAdminIdentity'])) {
    if ($_SESSION['LoginToAdminIdentityID'] != 18) {
        require '../init.php';
        $id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;

        // Select all data depend on this ID
        $query = 'SELECT * FROM identityalmkhtar WHERE id = ? LIMIT 1';
        $row = $db->Fetch($query, [$id]);
        // The row count
        $count = $db->RowCountData($query, [$id]);
        // If there's such ID show the form
        if ($count > 0) {
?>
            <!doctype html>
            <html lang="en">

            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>تسجيل بصمة</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
            </head>

            <body>

                <div class="text-center position-relative mt-5">
                    <h3><?php echo $row['fullname']; ?></h3>
                    <hr>
                    <input type="hidden" id="getid_fingerprint" value="<?php echo $id; ?>">
                    <div class="messageStatusFinger text-center">

                    </div>
                    <div id="appendimg" style="padding: 32px;">
                        <?php
                        if (empty($row['fingerprint'])) {
                            echo '<img src="1_ZGWXMiqjVcnuRXdytvgwmA.png" width="100" alt="">';
                        } else {
                            echo '<img src="uploads/fingerprint/' . $row['fingerprint'] . '" width="100" alt="">';
                        }
                        ?>

                    </div>
                    <div class="fingerPrintTemplate d-none"></div>
                    <div class="text-center stepfinger">
                        <?php
                        if (!empty($row['fingerprint'])) {
                            echo '<h2>يوجد بصمة</h2>';
                        }
                        ?>
                    </div>
                    <button type="button" class="btn btn-outline-primary mt-3" disabled id="startfinger">تسجيل بصمة جديدة</button>
                </div>
                </div>
                <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
                <script src="finger.js"></script>
            </body>

            </html>
<?php
        } else {
            header('Location:index.php');
            exit();
        }
    } else {
        header('Location:index.php');
        exit();
    }
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
