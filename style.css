/*
* <PERSON>o <PERSON> (Arabic) - Modern Flat Design
*/
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

@font-face {
  font-family: 'Al Hurra Txt Bold';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot);
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot?#iefix)
      format('embedded-opentype'),
    url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.woff2) format('woff2'),
    url(../fonts/Al-Hurra/AlHurraTxtBold.woff) format('woff'),
    url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.ttf) format('truetype');
}

/* Modern Flat Design Variables */
:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --success-color: #27ae60;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --purple-color: #9b59b6;
  --orange-color: #fd7e14;
  --background-light: #f4f6f9;
  --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  font-family: 'Cairo', 'Noto Kufi Arabic', sans-serif !important;
  -webkit-print-color-adjust: exact;
  color: var(--dark-color);
  line-height: 1.6;
  min-height: 100vh;
}
/* Modern Container Styles */
.container-fluid {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 30px;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--border-radius);
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: var(--card-shadow);
  border: none;
}

.header h4,
.header h6 {
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Canvas Styles */
canvas {
  position: absolute;
}

#appendCanvas {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ltr;
}

/* Modern Table Styles */
.table {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
  margin-bottom: 0;
}

.table thead th {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-align: center;
  padding: 15px 10px;
  font-size: 14px;
  position: relative;
}

.table thead th:hover {
  background: linear-gradient(135deg, #34495e 0%, var(--secondary-color) 100%);
  cursor: pointer;
}

.table tbody tr {
  transition: var(--transition);
  border: none;
}

.table tbody tr:hover {
  background: rgba(52, 152, 219, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table tr td,
.table tr th {
  vertical-align: middle !important;
  border: none;
  padding: 12px 8px;
  text-align: center;
}

.table tbody tr:nth-child(even) {
  background: rgba(248, 249, 250, 0.5);
}

.table-responsive {
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  background: white;
  padding: 0;
}
/* Modern Button Styles */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 8px 16px;
  transition: var(--transition);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

/* Button Color Coding */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, #138496 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
}

/* Purple button for fingerprint */
.btn-purple {
  background: linear-gradient(135deg, var(--purple-color) 0%, #8e44ad 100%);
  color: white;
  border: none;
}

.btn-purple:hover {
  background: linear-gradient(135deg, #8e44ad 0%, var(--purple-color) 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

/* Orange button for face print */
.btn-orange {
  background: linear-gradient(135deg, var(--orange-color) 0%, #e67e22 100%);
  color: white;
  border: none;
}

.btn-orange:hover {
  background: linear-gradient(135deg, #e67e22 0%, var(--orange-color) 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
}

/* Enhanced button hover effects */
.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9 0%, var(--primary-color) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c0392b 0%, var(--danger-color) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #229954 0%, var(--success-color) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-info:hover {
  background: linear-gradient(135deg, #138496 0%, var(--info-color) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-warning:hover {
  background: linear-gradient(135deg, #e67e22 0%, var(--warning-color) 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

/* File Upload Styles */
.file-upload {
  background: white;
  width: 263px;
  margin: 0 auto;
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

.file-upload-btn {
  width: 100%;
  margin: 0;
  color: white;
  background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
  border: none;
  padding: 12px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  outline: none;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-upload-btn:hover {
  background: #1aa059;
  color: #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.file-upload-btn:active {
  border: 0;
  transition: all 0.2s ease;
}

.file-upload-content {
  display: none;
  text-align: center;
}

.file-upload-input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}

.image-upload-wrap {
  margin-top: 20px;
  border: 4px dashed #1fb264;
  position: relative;
}

.image-dropping,
.image-upload-wrap:hover {
  background-color: #1fb264;
  border: 4px dashed #ffffff;
}

.image-title-wrap {
  padding: 0 15px 15px 15px;
  color: #222;
}

.drag-text {
  text-align: center;
}

.drag-text h3 {
  font-weight: 100;
  text-transform: uppercase;
  color: #15824b;
  padding: 60px 0;
}

.file-upload-image {
  max-height: 200px;
  max-width: 200px;
  margin: auto;
  padding: 20px;
}

.remove-image {
  width: 200px;
  margin: 0;
  color: #fff;
  background: #6e72ea;
  border: none;
  padding: 10px;
  border-radius: 4px;
  border-bottom: 4px solid #1f23ab;
  transition: all 0.2s ease;
  outline: none;
}

.remove-image:hover {
  background: #c13b2a;
  color: #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.remove-image:active {
  border: 0;
  transition: all 0.2s ease;
}
@media (max-width: 767px) {
  .file-upload {
    position: inherit !important;
  }
}
.login {
  max-width: 453px;
  margin: 10px auto;
  background-color: #b0955e45;
  padding: 35px;
  border-radius: 10px;
}
.img-login img {
  max-width: 400px;
}
.header {
  border-bottom: 2px solid #777;
  padding: 10px;
}
.page-item {
  font-size: 12px;
}
.hint-text {
  font-size: 13px;
  padding-bottom: 8px;
  padding-top: 8px;
}
.header {
  border-bottom: 2px solid #777;
  padding: 11px;
  margin-bottom: 15px;
}
.header h4:first-child {
  margin-top: -4px;
  line-height: 3;
}
.customDiv {
  text-align: center;
  margin-top: 50px;
  border: 2px solid;
  padding: 86px;
}
.customDiv:first-child {
  margin-top: 0 !important;
  border: none !important;
}
#showErrors li:last-child {
  display: none;
}
.inputSize .form-control {
  padding: 5px !important;
}

/* Modern Form Styles */
.form-control {
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  padding: 12px 15px;
  transition: var(--transition);
  background: white;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  background: white;
}

.form-group label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 8px;
}

/* Advanced Search Styles */
.advancedSearch {
  background: white;
  border: none;
  padding: 25px;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  position: relative;
  margin-bottom: 25px;
}

.advancedSearch::before {
  content: 'البحث المتقدم';
  position: absolute;
  top: -12px;
  right: 20px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
  color: white;
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

.spanSearch {
  position: absolute;
  top: -17px;
  right: 29px;
}

/* Modal Styles */
.modal-content {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border: none;
}

.modal-title {
  font-weight: 600;
}

.modal-body {
  padding: 25px;
}

.modal-footer {
  border: none;
  padding: 20px 25px;
}

/* Pagination Styles */
.pagination {
  margin: 20px 0;
}

.page-item .page-link {
  border: none;
  margin: 0 2px;
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}

.page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.page-item .page-link:hover {
  background: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* Hint Text */
.hint-text {
  background: rgba(52, 152, 219, 0.1);
  padding: 10px 15px;
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 15px;
  border-right: 4px solid var(--primary-color);
}

/* Sidebar Buttons */
.sidebar-buttons {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-buttons .btn {
  min-width: 150px;
  justify-content: flex-start;
  text-align: right;
  padding: 12px 20px;
}

/* Animation Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Loading Animation */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    width: 95% !important;
    padding: 15px;
    margin-top: 10px;
  }

  .header {
    padding: 15px;
    text-align: center;
  }

  .header .row {
    flex-direction: column-reverse;
  }

  .header .col-md-6 {
    text-align: center !important;
  }

  .sidebar-buttons {
    position: relative;
    left: auto;
    top: auto;
    transform: none;
    flex-direction: row;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .sidebar-buttons .btn {
    min-width: auto;
    margin: 5px;
    font-size: 12px;
    padding: 8px 12px;
  }

  .advancedSearch {
    padding: 15px;
  }

  .table-responsive {
    font-size: 12px;
  }

  .btn-sm {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .container-fluid {
    width: 98% !important;
    padding: 10px;
  }

  .header h4 {
    font-size: 1.1rem;
  }

  .header h6 {
    font-size: 0.9rem;
  }

  .sidebar-buttons .btn {
    font-size: 11px;
    padding: 6px 10px;
  }

  .table thead th {
    font-size: 11px;
    padding: 8px 4px;
  }

  .table tbody td {
    font-size: 11px;
    padding: 6px 4px;
  }
}

/* Print Styles */
@media print {
  .sidebar-buttons,
  .header .btn,
  .pagination {
    display: none !important;
  }

  body {
    background: white !important;
  }

  .container-fluid {
    background: white !important;
    box-shadow: none !important;
  }

  .table {
    box-shadow: none !important;
  }
}

/* Modern Enhancements */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Button Loading State */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Table Enhancements */
.table tbody tr.table-active {
  background: rgba(52, 152, 219, 0.1) !important;
  border-left: 4px solid var(--primary-color);
}

.table tbody tr.table-hover-active {
  background: rgba(52, 152, 219, 0.05) !important;
  transform: translateX(-2px);
}

.table thead th.sort-asc::after {
  content: '\f0de';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  margin-left: 5px;
  color: var(--primary-color);
}

.table thead th.sort-desc::after {
  content: '\f0dd';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  margin-left: 5px;
  color: var(--primary-color);
}

/* Form Enhancements */
.form-control.has-value,
.form-control.search-active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-group label.floating {
  transform: translateY(-20px) scale(0.85);
  color: var(--primary-color);
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 400px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-info {
  border-left-color: var(--info-color);
}

.notification-content {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: none;
  z-index: 1000;
  transition: var(--transition);
}

.scroll-to-top.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-to-top:hover {
  transform: translateY(-3px);
}

/* Theme Toggle */
.theme-toggle {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark Theme (Optional) */
body.dark-theme {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

body.dark-theme .container-fluid {
  background: rgba(44, 62, 80, 0.95);
  color: #ecf0f1;
}

body.dark-theme .table {
  background: #34495e;
  color: #ecf0f1;
}

body.dark-theme .table thead th {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

body.dark-theme .form-control {
  background: #34495e;
  border-color: #4a6741;
  color: #ecf0f1;
}

body.dark-theme .advancedSearch {
  background: #34495e;
  color: #ecf0f1;
}
