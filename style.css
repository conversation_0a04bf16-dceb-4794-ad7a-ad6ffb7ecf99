/* 
* Noto <PERSON> (Arabic)
*/
@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

@font-face {
    font-family: 'Al Hurra Txt Bold';
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot);
    src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot?#iefix) format('embedded-opentype'), url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.woff2) format('woff2'), url(../fonts/Al-Hurra/AlHurraTxtBold.woff) format('woff'), url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.ttf) format('truetype');
}

body {
  background-color: #ecf0f1;
  background-image: url(bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position-x: right;
  font-family: 'Noto <PERSON> Arabic', sans-serif !important;
  -webkit-print-color-adjust:exact;
}
canvas {
  position: absolute;
}
#appendCanvas
{
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ltr;
}
.table tr td, .table tr th {
  vertical-align: middle!important;
}
.file-upload {
    background-color: #ffffff;
    width: 263px;
    margin: 0 auto;
    padding: 14px;
  }
  .file-upload-btn {
    width: 100%;
    margin: 0;
    color: #fff;
    background: #1FB264;
    border: none;
    padding: 10px;
    border-radius: 4px;
    border-bottom: 4px solid #15824B;
    transition: all .2s ease;
    outline: none;
    text-transform: uppercase;
    font-weight: 700;
  }
  
  .file-upload-btn:hover {
    background: #1AA059;
    color: #ffffff;
    transition: all .2s ease;
    cursor: pointer;
  }
  
  .file-upload-btn:active {
    border: 0;
    transition: all .2s ease;
  }
  
  .file-upload-content {
    display: none;
    text-align: center;
  }
  
  .file-upload-input {
    position: absolute;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    outline: none;
    opacity: 0;
    cursor: pointer;
  }
  
  .image-upload-wrap {
    margin-top: 20px;
    border: 4px dashed #1FB264;
    position: relative;
  }
  
  .image-dropping,
  .image-upload-wrap:hover {
    background-color: #1FB264;
    border: 4px dashed #ffffff;
  }
  
  .image-title-wrap {
    padding: 0 15px 15px 15px;
    color: #222;
  }
  
  .drag-text {
    text-align: center;
  }
  
  .drag-text h3 {
    font-weight: 100;
    text-transform: uppercase;
    color: #15824B;
    padding: 60px 0;
  }
  
  .file-upload-image {
    max-height: 200px;
    max-width: 200px;
    margin: auto;
    padding: 20px;
  }
  
  .remove-image {
    width: 200px;
    margin: 0;
    color: #fff;
    background: #6e72ea;
    border: none;
    padding: 10px;
    border-radius: 4px;
    border-bottom: 4px solid #1f23ab;
    transition: all .2s ease;
    outline: none;
  }
  
  .remove-image:hover {
    background: #c13b2a;
    color: #ffffff;
    transition: all .2s ease;
    cursor: pointer;
  }
  
  .remove-image:active {
    border: 0;
    transition: all .2s ease;
  }
  @media (max-width: 767px) {
      .file-upload {
          position: inherit!important;
      }
  }
.login
{
    max-width: 453px;
    margin: 10px auto;
    background-color: #b0955e45;
    padding: 35px;
    border-radius: 10px;
}
.img-login img 
{
    max-width: 400px;
}
.header
{
    border-bottom: 2px solid #777;
    padding: 10px;
}
.page-item
{
    font-size: 12px;
}
.hint-text
{
    font-size: 13px;
    padding-bottom: 8px;
    padding-top: 8px;
}
.header
{
    border-bottom: 2px solid #777;
    padding: 11px;
    margin-bottom: 15px;
}
.header h4:first-child 
{
    margin-top: -4px;
    line-height: 3;
}
.customDiv
{
    text-align: center;
    margin-top: 50px;
    border: 2px solid;
    padding: 86px;
}
.customDiv:first-child
{
    margin-top: 0!important;
    border: none!important;
}
#showErrors li:last-child{
    display: none;
}
.inputSize .form-control {
    padding: 5px!important;
}

.advancedSearch{
    border: 1px solid #777;
    padding: 22px;
    border-radius: 20px;
    position: relative;
}
.spanSearch
{
    position: absolute;
    top: -17px;
    right: 29px;
}
.table td, .table th {
    padding: 1px!important;
}
.table thead th {
  font-size: 13px!important;
}