$(function () {
  'use strict';
  $('[data-toggle="tooltip"]').tooltip();
  $('#formreset').on('click', function (e) {
    e.preventDefault();
    $('#search_form').trigger('reset');
    load_data(1);
  });
  load_data(1);

  function load_data(
    page,
    query = '',
    query2 = '',
    query3 = '',
    query4 = '',
    query5 = '',
    query6 = ''
  ) {
    $.ajax({
      url: 'fetch.php',
      method: 'POST',
      data: {
        page: page,
        query: query,
        query2: query2,
        query3: query3,
        query4: query4,
        query5: query5,
        query6: query6,
      },
      success: function (data) {
        $('#dynamic_content').html(data);
      },
    });
  }

  $(document).on('click', '.page-link', function () {
    var page = $(this).data('page_number'),
      query = $('#label1').val(),
      query2 = $('#label2').val(),
      query3 = $('#label3').val(),
      query4 = $('#label4').val(),
      query5 = $('#label5').val(),
      query6 = $('#label6').val();
    load_data(page, query, query2, query3, query4, query5, query6);
  });

  $('#search_form').bind('keyup change', function () {
    var query = $('#label1').val(),
      query2 = $('#label2').val(),
      query3 = $('#label3').val(),
      query4 = $('#label4').val(),
      query5 = $('#label5').val(),
      query6 = $('#label6').val();
    load_data(1, query, query2, query3, query4, query5, query6);
  });
  $(document).on('click', '.fetchpic', function () {
    $.ajax({
      type: 'POST',
      url: 'imgtobase64.php',
      data: { img: $('#fetchImg').attr('src') },
      beforeSend: function () {
        $('.fetchpic').html('<img src="pleasewait.gif">');
      },
      success: function (data) {
        $('#download-photo').attr('src', data);
        $('.fetchpic').html('جلب الصورة الشخصية');
      },
    });
  });
});
function readURL(input) {
  'use strict';
  if (input.files && input.files[0]) {
    var reader = new FileReader();

    reader.onload = function (e) {
      $('.image-upload-wrap').hide();

      $('.file-upload-image').attr('src', e.target.result);
      $('.file-upload-content').show();

      $('.image-title').html(input.files[0].name);
    };

    reader.readAsDataURL(input.files[0]);
  } else {
    removeUpload();
  }
}

function removeUpload() {
  'use strict';
  let confirmRemove = confirm('هل انت متأكد؟');
  if (confirmRemove == true) {
    $('.file-upload-input').replaceWith($('.file-upload-input').clone());
    $('.file-upload-content').hide();
    $('.image-upload-wrap').show();
  }
}
$('.image-upload-wrap').bind('dragover', function () {
  $('.image-upload-wrap').addClass('image-dropping');
});
$('.image-upload-wrap').bind('dragleave', function () {
  $('.image-upload-wrap').removeClass('image-dropping');
});

function showMyImage(fileInput) {
  'use strict';
  document.getElementById('thumbnil').style.display = '';
  document.getElementById('showbotton').style.display = '';
  var files = fileInput.files;
  for (var i = 0; i < files.length; i++) {
    var file = files[i];
    var imageType = /image.*/;
    if (!file.type.match(imageType)) {
      continue;
    }
    var img = document.getElementById('thumbnil');
    img.file = file;
    var reader = new FileReader();
    reader.onload = (function (aImg) {
      return function (e) {
        aImg.src = e.target.result;
      };
    })(img);
    reader.readAsDataURL(file);
  }
}
